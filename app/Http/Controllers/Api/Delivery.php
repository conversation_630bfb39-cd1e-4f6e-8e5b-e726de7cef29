<?php

namespace App\Http\Controllers\Api;

use App\Models\Settings;

/**
 * Delivery Utility Class
 *
 * Provides utility methods for managing delivery settings and functionality.
 * This class handles delivery-related operations and settings retrieval.
 */
class Delivery
{
    /**
     * Get Delivery Setting Value
     *
     * Retrieves the current delivery setting value from the database.
     * This determines whether delivery service is enabled or disabled.
     *
     * @return string|null The delivery setting value ("true" or "false") or null if not set
     */
    public static function getDelivery()
    {
        return Settings::where("key", "delivery")->value("value");
    }
}
