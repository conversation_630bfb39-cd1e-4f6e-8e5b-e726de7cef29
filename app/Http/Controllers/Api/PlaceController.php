<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Place;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Knuckles\Scribe\Attributes\Authenticated;
use <PERSON>nuckle<PERSON>\Scribe\Attributes\Unauthenticated;

class PlaceController extends Controller
{
    /**
     * Get Places List
     *
     * Retrieves a list of all active places where orders can be delivered or picked up.
     * Places include information about their visibility to different user types
     * (employees vs residents) and location details.
     *
     * @response 200 scenario="Places retrieved successfully" {
     *   "data": [
     *     {
     *       "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "name": "Main Counter",
     *       "location": "Ground Floor, Main Building",
     *       "visible_to_employee": true,
     *       "visible_to_resident": true
     *     },
     *     {
     *       "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5h",
     *       "name": "Office Building Lobby",
     *       "location": "Office Building, First Floor",
     *       "visible_to_employee": true,
     *       "visible_to_resident": false
     *     }
     *   ]
     * }
     *
     * @responseField data array Array of active places
     * @responseField data[].id string UUID of the place
     * @responseField data[].name string Name of the place
     * @responseField data[].location string Physical location description
     * @responseField data[].visible_to_employee boolean Whether the place is visible to employees
     * @responseField data[].visible_to_resident boolean Whether the place is visible to residents
     *
     * @group Places
     */
    #[unauthenticated]
    public function placesList(Request $request)
    {
        $places = Place::where("status", "Active")
            ->select([
                "id",
                "name",
                "location",
                "visible_to_employee",
                "visible_to_resident",
            ])
            ->get();
        return response()->json($places);
    }
}
