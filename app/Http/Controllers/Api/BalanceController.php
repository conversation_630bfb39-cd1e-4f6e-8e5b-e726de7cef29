<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Balance;
use App\Models\Plan;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Authenticated;

class BalanceController extends Controller
{
    /**
     * Get My Balance
     *
     * Retrieves the current wallet balance and active subscription information for the
     * authenticated user. Returns wallet balance in decimal format and subscription
     * details if the user has an active subscription.
     *
     * @response 200 scenario="User with wallet balance only" {
     *   "balance": 25.50
     * }
     *
     * @response 200 scenario="User with subscription and wallet balance" {
     *   "balance": 25.50,
     *   "subscription": {
     *     "name": "Premium Monthly Plan",
     *     "endsAt": "2024-02-15T23:59:59.000000Z",
     *     "how_long": "15 days remaining"
     *   }
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @responseField balance number Current wallet balance in decimal format (converted from cents)
     * @responseField subscription object Subscription information (only present if user has active subscription)
     * @responseField subscription.name string Name of the active subscription plan
     * @responseField subscription.endsAt string ISO 8601 timestamp when the subscription expires
     * @responseField subscription.how_long string Human-readable time remaining until subscription expires
     *
     * @group Profile
     */
    #[Authenticated]
    public function myBalance(Request $request)
    {
        if (
            $request
                ->user()
                ->subscription()
                ->first() == null
        ) {
            return response()->json([
                "balance" => $request->user()->current_balance() / 100,
            ]);
        } else {
            $subscription = [
                "name" => $request->user()->lastSubscription()->plan->name,
                "endsAt" => $request->user()->lastSubscription()->expired_at,
                "how_long" => $request
                    ->user()
                    ->lastSubscription()
                    ->getHowLong(),
            ];
            return response()->json([
                "balance" => $request->user()->current_balance() / 100,
                "subscription" => $subscription,
            ]);
        }
    }

    /**
     * Get Balance History
     *
     * Retrieves a paginated history of all balance transactions for the authenticated user.
     * Includes deposits, charges, refunds, and other balance-affecting activities.
     * Transactions are ordered by most recent first.
     *
     * @queryParam page integer optional Page number for pagination (minimum: 1). Example: 1
     * @queryParam per_page integer optional Number of items per page (minimum: 1, default: 10). Example: 10
     *
     * @response 200 scenario="Balance history retrieved successfully" {
     *   "data": [
     *     {
     *       "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "user_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "amount": -5.50,
     *       "action_type": "charge",
     *       "invoice_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "created_at": "2024-01-15T09:00:00.000000Z",
     *       "updated_at": "2024-01-15T09:00:00.000000Z"
     *     },
     *     {
     *       "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5h",
     *       "user_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "amount": 25.00,
     *       "action_type": "deposit",
     *       "invoice_id": null,
     *       "created_at": "2024-01-14T10:00:00.000000Z",
     *       "updated_at": "2024-01-14T10:00:00.000000Z"
     *     }
     *   ],
     *   "links": {
     *     "first": "http://example.com/api/balance/history?page=1",
     *     "last": "http://example.com/api/balance/history?page=5",
     *     "prev": null,
     *     "next": "http://example.com/api/balance/history?page=2"
     *   },
     *   "meta": {
     *     "current_page": 1,
     *     "from": 1,
     *     "last_page": 5,
     *     "per_page": 10,
     *     "to": 10,
     *     "total": 50
     *   }
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The page field must be at least 1.",
     *   "errors": {
     *     "page": ["The page field must be at least 1."],
     *     "per_page": ["The per page field must be at least 1."]
     *   }
     * }
     *
     * @responseField data array Array of balance transactions
     * @responseField data[].id string UUID of the balance transaction
     * @responseField data[].user_id string UUID of the user
     * @responseField data[].amount number Transaction amount in decimal format (negative for charges, positive for deposits)
     * @responseField data[].action_type string Type of transaction (charge, deposit, refund, etc.)
     * @responseField data[].invoice_id string|null UUID of associated invoice (if applicable)
     * @responseField data[].created_at string ISO 8601 timestamp of transaction creation
     * @responseField data[].updated_at string ISO 8601 timestamp of last update
     *
     * @group Profile
     */
    public function balanceHistory(Request $request)
    {
        $validatedData = Validator::make(
            [
                "page" => $request->page,
                "per_page" => $request->per_page,
            ],
            [
                "page" => "nullable|integer|min:1",
                "per_page" => "nullable|integer|min:1",
            ]
        )->validate();

        $user = $request->user();

        $activities = Balance::where("user_id", $user->id)
            ->orderByDesc("created_at")
            ->paginate($validatedData["per_page"] ?? 10);

        $activities->each(function ($activity) {
            $activity->amount = $activity->amount / 100;
        });

        return response()->json($activities);
    }

    /**
     * Get Subscription Details
     *
     * Retrieves detailed information about the authenticated user's current subscription
     * including plan details, features, balances, and expiration information.
     * Returns wallet balance if no active subscription exists.
     *
     * @response 200 scenario="User without subscription" {
     *   "balance": 25.50
     * }
     *
     * @response 200 scenario="User with active subscription" {
     *   "plan_name": "Premium Monthly Plan",
     *   "plan_description": "Access to all premium features with monthly billing",
     *   "periodicity_type": "month",
     *   "canceled_at": null,
     *   "started_at": "2024-01-01T00:00:00.000000Z",
     *   "expired_at": "2024-02-01T00:00:00.000000Z",
     *   "how_long": "15 days remaining",
     *   "features": [
     *     {
     *       "name": "Coffee Credits",
     *       "periodicity_type": "month",
     *       "variation_id": null,
     *       "charge": 50.00,
     *       "balance": 35.50
     *     },
     *     {
     *       "name": "Espresso Special",
     *       "periodicity_type": "month",
     *       "variation_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "charge": 20.00,
     *       "balance": 15.00
     *     }
     *   ]
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @responseField balance number Current wallet balance (only when no subscription exists)
     * @responseField plan_name string Name of the subscription plan
     * @responseField plan_description string Description of the subscription plan
     * @responseField periodicity_type string Billing period type (month, year, etc.)
     * @responseField canceled_at string|null ISO 8601 timestamp when subscription was canceled (null if active)
     * @responseField started_at string ISO 8601 timestamp when subscription started
     * @responseField expired_at string ISO 8601 timestamp when subscription expires
     * @responseField how_long string Human-readable time remaining until expiration
     * @responseField features array Array of subscription features and their balances
     * @responseField features[].name string Name of the feature
     * @responseField features[].periodicity_type string Feature billing period
     * @responseField features[].variation_id string|null UUID of associated product variation (if feature-specific)
     * @responseField features[].charge number Amount charged for this feature
     * @responseField features[].balance number Remaining balance for this feature
     *
     * @group Profile
     */
    #[Authenticated]
    public function subscriptionDetails(Request $request)
    {
        if (
            auth()
                ->user()
                ->lastSubscription() == null
        ) {
            return response()->json([
                "balance" =>
                    auth()
                        ->user()
                        ->current_balance() / 100,
            ]);
        } else {
            $subscription = auth()
                ->user()
                ->lastSubscription();
            $feat = [];
            $subscription->plan->features->each(function ($feature) use (
                &$feat
            ) {
                $item = [
                    "name" => $feature->name,
                    "periodicity_type" => $feature->periodicity_type,
                    "variation_id" => $feature->variation_id,
                    "charge" => $feature->pivot->charges / 100,
                    "balance" =>
                        auth()
                            ->user()
                            ->balance($feature->name) / 100,
                ];
                $feat[] = $item;
            });

            return response()->json([
                "plan_name" => $subscription->plan->name,
                "plan_description" => $subscription->plan->description,
                "periodicity_type" => $subscription->plan->periodicity_type,
                "canceled_at" => $subscription->canceled_at,
                "started_at" => $subscription->started_at,
                "expired_at" => $subscription->expired_at,
                "how_long" => $subscription->getHowLong(),
                "features" => $feat,
            ]);
        }
    }

    /**
     * Get Subscription History
     *
     * Retrieves a paginated history of all subscriptions for the authenticated user,
     * including past and current subscriptions with their features and status.
     * Subscriptions are ordered by expiration date (most recent first).
     *
     * @queryParam page integer optional Page number for pagination (minimum: 1). Example: 1
     * @queryParam per_page integer optional Number of items per page (minimum: 1, default: 10). Example: 10
     *
     * @response 200 scenario="Subscription history retrieved successfully" {
     *   "data": [
     *     {
     *       "started_at": "2024-01-01T00:00:00.000000Z",
     *       "expired_at": "2024-02-01T00:00:00.000000Z",
     *       "canceled_at": null,
     *       "plan_name": "Premium Monthly Plan",
     *       "plan_description": "Access to all premium features with monthly billing",
     *       "periodicity_type": "month",
     *       "is_active": true,
     *       "features": [
     *         {
     *           "name": "Coffee Credits",
     *           "periodicity_type": "month",
     *           "charge": 50.00,
     *           "balance": 35.50
     *         }
     *       ]
     *     },
     *     {
     *       "started_at": "2023-12-01T00:00:00.000000Z",
     *       "expired_at": "2024-01-01T00:00:00.000000Z",
     *       "canceled_at": "2023-12-25T00:00:00.000000Z",
     *       "plan_name": "Basic Monthly Plan",
     *       "plan_description": "Basic access with monthly billing",
     *       "periodicity_type": "month",
     *       "is_active": false,
     *       "features": [
     *         {
     *           "name": "Basic Credits",
     *           "periodicity_type": "month",
     *           "charge": 25.00,
     *           "balance": 0.00
     *         }
     *       ]
     *     }
     *   ],
     *   "links": {
     *     "first": "http://example.com/api/profile/subscription/history?page=1",
     *     "last": "http://example.com/api/profile/subscription/history?page=3",
     *     "prev": null,
     *     "next": "http://example.com/api/profile/subscription/history?page=2"
     *   },
     *   "meta": {
     *     "current_page": 1,
     *     "from": 1,
     *     "last_page": 3,
     *     "per_page": 10,
     *     "to": 10,
     *     "total": 25
     *   }
     * }
     *
     * @response 200 scenario="No subscriptions found" {
     *   "message": "No subscriptions found"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The page field must be at least 1.",
     *   "errors": {
     *     "page": ["The page field must be at least 1."],
     *     "per_page": ["The per page field must be at least 1."]
     *   }
     * }
     *
     * @responseField data array Array of subscription records
     * @responseField data[].started_at string ISO 8601 timestamp when subscription started
     * @responseField data[].expired_at string ISO 8601 timestamp when subscription expires/expired
     * @responseField data[].canceled_at string|null ISO 8601 timestamp when subscription was canceled
     * @responseField data[].plan_name string Name of the subscription plan
     * @responseField data[].plan_description string Description of the subscription plan
     * @responseField data[].periodicity_type string Billing period type (month, year, etc.)
     * @responseField data[].is_active boolean Whether this subscription is currently active
     * @responseField data[].features array Array of features included in this subscription
     * @responseField data[].features[].name string Name of the feature
     * @responseField data[].features[].periodicity_type string Feature billing period
     * @responseField data[].features[].charge number Amount charged for this feature
     * @responseField data[].features[].balance number Current balance for this feature
     *
     * @group Profile
     */
    #[Authenticated]
    public function subscriptionHistory(Request $request)
    {
        $validatedData = Validator::make(
            [
                "page" => $request->page,
                "per_page" => $request->per_page,
            ],
            [
                "page" => "nullable|integer|min:1",
                "per_page" => "nullable|integer|min:1",
            ]
        )->validate();

        $user = auth()->user();

        $activeSubscription = $user->lastSubscription();

        $subscriptions = Subscription::withoutGlobalScopes()
            ->where("subscriber_id", $user->id)
            ->orderByDesc("expired_at");
        if ($subscriptions->count() == 0) {
            return response()->json(["message" => "No subscriptions found"]);
        }

        $subscriptions = $subscriptions->paginate(
            $validatedData["per_page"] ?? 10
        );

        $subscriptions->each(function ($subscription) use (
            $activeSubscription,
            $user
        ) {
            $subscription->setAttribute("plan_name", $subscription->plan->name);
            $subscription->setAttribute(
                "plan_description",
                $subscription->plan->description
            );
            $subscription->setAttribute(
                "periodicity_type",
                $subscription->plan->periodicity_type
            );
            if ($activeSubscription != null) {
                $subscription->setAttribute(
                    "is_active",
                    $activeSubscription->id == $subscription->id
                );
            } else {
                $subscription->setAttribute("is_active", false);
            }
            $subscription->setAttribute(
                "features",
                $subscription->plan->features->each(function ($feature) use (
                    $user
                ) {
                    $feature->setAttribute(
                        "charge",
                        $feature->pivot->charges / 100
                    );
                    $feature->setAttribute(
                        "balance",
                        $user->balance($feature->name) / 100
                    );
                    $feature->makeHidden([
                        "id",
                        "consumable",
                        "quota",
                        "postpaid",
                        "periodicity",
                        "variation_id",
                        "deleted_at",
                        "created_at",
                        "updated_at",
                        "pivot",
                    ]);
                })
            );
            $subscription->makeHidden([
                "plan_id",
                "id",
                "deleted_at",
                "plan",
                "grace_days_ended_at",
                "suppressed_at",
                "was_switched",
                "created_at",
                "updated_at",
                "subscriber_type",
                "subscriber_id",
            ]);
        });

        return response()->json($subscriptions);
    }
}
