<?php

namespace App\Http\Controllers\Api;

use App\Events\Delivery;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Place;
use App\Models\Settings;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Knuckles\Scribe\Attributes\Authenticated;
use App\Http\Controllers\Api\Delivery as DeliveryVal;

class SettingsController extends Controller
{
    /**
     * Get Delivery Setting
     *
     * Retrieves the current delivery setting which determines whether waiters
     * deliver orders to customers or if customers need to pick up orders themselves.
     *
     * @response 200 scenario="Delivery setting retrieved successfully" {
     *   "value": true
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @responseField value boolean Whether delivery service is enabled (true) or disabled (false)
     *
     * @group Settings
     */
    #[Authenticated]
    public function getDeliveryValue()
    {
        /*$deliveryValue = Settings::where("key", "delivery")->value("value");*/

        return response()->json(["value" => DeliveryVal::getDelivery()]);
    }

    /**
     * Update Delivery Setting
     *
     * Updates the delivery setting to enable or disable waiter delivery service.
     * When enabled, waiters will deliver orders to customers. When disabled,
     * customers must pick up their orders. This setting affects the entire system.
     *
     * @bodyParam value boolean required Whether to enable (true) or disable (false) delivery service. Example: true
     *
     * @response 200 scenario="Delivery setting updated successfully" {
     *   "data": "true",
     *   "message": "Delivery setting updated successfully"
     * }
     *
     * @response 404 scenario="Delivery setting not found" {
     *   "message": "Delivery setting not found"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The value field is required.",
     *   "errors": {
     *     "value": ["The value field is required."]
     *   }
     * }
     *
     * @responseField data string The updated delivery setting value as string ("true" or "false")
     * @responseField message string Success message confirming the update
     *
     * @group Settings
     */
    #[Authenticated]
    public function updateDeliveryValue(Request $request)
    {
        $request->validate([
            "value" => "required|boolean",
        ]);

        $deliverySetting = Settings::where("key", "delivery")->first();

        if (!$deliverySetting) {
            return response()->json(
                ["message" => "Delivery setting not found"],
                404
            );
        }

        $deliverySetting->value = $request->input("value") ? "true" : "false";
        $deliverySetting->save();

        event(new Delivery($deliverySetting->value));
        return response()->json([
            "data" => $deliverySetting->value,
            "message" => "Delivery setting updated successfully",
        ]);
    }

    /**
     * Get Order Statistics
     *
     * Retrieves comprehensive order statistics for a specific date, including
     * total orders and breakdown by status. This endpoint provides insights
     * into daily order performance and status distribution.
     *
     * @queryParam date string required The date to get statistics for in Y-m-d format. Example: 2024-01-15
     *
     * @response 200 scenario="Order statistics retrieved successfully" {
     *   "total_orders": 45,
     *   "pending_orders": 3,
     *   "accepted_orders": 8,
     *   "rejected_orders": 2,
     *   "cancelled_orders": 1,
     *   "completed_orders": 31
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The date does not match the format Y-m-d.",
     *   "errors": {
     *     "date": ["The date does not match the format Y-m-d."]
     *   }
     * }
     *
     * @responseField total_orders integer Total number of orders for the specified date
     * @responseField pending_orders integer Number of orders with pending status
     * @responseField accepted_orders integer Number of orders with accepted status
     * @responseField rejected_orders integer Number of orders with rejected status
     * @responseField cancelled_orders integer Number of orders with cancelled status
     * @responseField completed_orders integer Number of orders with completed status
     *
     * @group Settings
     */
    #[Authenticated]
    public function getOerdersStatistics(Request $request)
    {
        $validatedData = Validator::make(
            [
                "date" => $request->date,
            ],
            [
                "date" => "required|date_format:Y-m-d",
            ]
        )->validate();

        $date = $validatedData["date"];

        $orderStatistics = Order::selectRaw(
            "
            COUNT(*) AS total_orders,
            CAST(COALESCE(SUM(status = 'pending'), 0) AS UNSIGNED) AS pending_orders,
            CAST(COALESCE(SUM(status = 'accepted'), 0) AS UNSIGNED) AS accepted_orders,
            CAST(COALESCE(SUM(status = 'rejected'), 0) AS UNSIGNED) AS rejected_orders,
            CAST(COALESCE(SUM(status = 'cancelled'), 0) AS UNSIGNED) AS cancelled_orders,
            CAST(COALESCE(SUM(status = 'completed'), 0) AS UNSIGNED) AS completed_orders
        "
        )
            ->when($date, function ($query, $date) {
                return $query->whereDate("created_at", $date);
            })
            ->first();

        return response()->json($orderStatistics);
    }

    /**
     * Get Pending and Processing Orders Count
     *
     * Retrieves the current count of orders that are pending or being processed.
     * This endpoint is useful for dashboard displays and real-time order monitoring
     * to show staff how many orders need attention.
     *
     * @response 200 scenario="Order counts retrieved successfully" {
     *   "pending_orders": 5,
     *   "accepted_orders": 12
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @responseField pending_orders integer Number of orders currently in pending status
     * @responseField accepted_orders integer Number of orders currently in accepted status (being processed)
     *
     * @group Settings
     */
    #[Authenticated]
    public function getPendingAndProcessingOrders()
    {
        $orders = Order::selectRaw(
            "
            CAST(COALESCE(SUM(status = 'pending'), 0) AS UNSIGNED) AS pending_orders,
            CAST(COALESCE(SUM(status = 'accepted'), 0) AS UNSIGNED) AS accepted_orders
        "
        )->first();

        return response()->json($orders);
    }

    /**
     * Get Application Settings
     *
     * Retrieves all public application settings including branding, colors, and
     * configuration values. This endpoint provides settings needed for frontend
     * customization and application appearance.
     *
     * @response 200 scenario="Settings retrieved successfully" {
     *   "app_title": "Lamah Cafe",
     *   "app_primary_color": "#3B82F6",
     *   "app_secondary_color": "#10B981",
     *   "app_accent_color": "#F59E0B",
     *   "app_privacy": "Your privacy policy content here...",
     *   "app_logo": "https://example.com/storage/logo.png"
     * }
     *
     * @responseField app_title string The application title/name
     * @responseField app_primary_color string|null Primary color for the application theme (hex color code)
     * @responseField app_secondary_color string|null Secondary color for the application theme (hex color code)
     * @responseField app_accent_color string|null Accent color for the application theme (hex color code)
     * @responseField app_privacy string|null Privacy policy content
     * @responseField app_logo string|null URL to the application logo image
     *
     * @group Settings
     */
    public function getSettings()
    {
        $settings = Settings::all();

        $return = [];

        $return["app_title"] = __("Lamah Cafe");

        foreach ($settings as $setting) {
            if ($setting->key === "app-primary-color") {
                $return["app_primary_color"] = $setting->value;
            }

            if ($setting->key === "app-secondary-color") {
                $return["app_secondary_color"] = $setting->value;
            }

            if ($setting->key === "app-accent-color") {
                $return["app_accent_color"] = $setting->value;
            }

            if ($setting->key === "app_privacy") {
                $return[$setting->key] = $setting->value;
            }

            if (
                $setting->key === "app_logo" and
                $setting->media()->count() > 0
            ) {
                $return[$setting->key] = $setting
                    ->media()
                    ->first()
                    ->getUrl();
            }
        }

        return response()->json($return);
    }
}
