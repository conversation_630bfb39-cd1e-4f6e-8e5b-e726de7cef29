<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Product;
use App\Models\Variation;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Authenticated;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Unauthenticated;

class ProductController extends Controller
{
    /**
     * Add Product to Favorites
     *
     * Adds a product to the authenticated user's favorites list. Each user can only
     * have one favorite entry per product to prevent duplicates.
     *
     * @urlParam product_id string required The UUID of the product to add to favorites. Example: 9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g
     *
     * @response 201 scenario="Successfully added to favorites" {
     *   "data": {
     *     "id": 123,
     *     "user_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *     "product_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *     "created_at": "2024-01-15T09:00:00.000000Z",
     *     "updated_at": "2024-01-15T09:00:00.000000Z"
     *   },
     *   "message": "Added to favorites"
     * }
     *
     * @response 409 scenario="Already in favorites" {
     *   "message": "Already in favorites"
     * }
     *
     * @response 400 scenario="Error occurred" {
     *   "message": "An error occurred during the process of adding to favorites",
     *   "error": {}
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Invalid product ID" {
     *   "message": "The product id field must be a valid UUID.",
     *   "errors": {
     *     "product_id": ["The product id field must be a valid UUID."]
     *   }
     * }
     *
     * @responseField data.id integer Auto-incrementing ID of the favorite record
     * @responseField data.user_id string UUID of the user who favorited the product
     * @responseField data.product_id string UUID of the favorited product
     * @responseField data.created_at string ISO 8601 timestamp of when the favorite was created
     * @responseField data.updated_at string ISO 8601 timestamp of when the favorite was last updated
     * @responseField message string Success or error message
     *
     * @group Products
     */
    #[Authenticated]
    public function addToFavorites(Request $request, string $product_id)
    {
        $user = auth()->user();
        $validatedData = Validator::make(
            [
                "product_id" => $product_id,
            ],
            [
                "product_id" => "required|UUID",
            ],
        )->validate();

        if (
            $user
                ->favoriteProducts()
                ->where("product_id", $validatedData["product_id"])
                ->exists()
        ) {
            return response()->json(["message" => "Already in favorites"], 409);
        }

        try {
            $fav = $user->favoriteProducts()->create([
                "product_id" => $validatedData["product_id"],
            ]);

            return response()->json(
                [
                    "data" => $fav,
                    "message" => "Added to favorites",
                ],
                201,
            );
        } catch (\Exception $e) {
            return response()->json(
                [
                    "message" =>
                        "An error occurred during the process of adding to favorites",
                    "error" => $e,
                ],
                400,
            );
        }
    }

    /**
     * Remove Product from Favorites
     *
     * Removes a product from the authenticated user's favorites list. The product
     * must be currently in the user's favorites to be removed.
     *
     * @urlParam product_id string required The UUID of the product to remove from favorites. Example: 9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g
     *
     * @response 200 scenario="Successfully removed from favorites" {
     *   "message": "Removed from favorites"
     * }
     *
     * @response 404 scenario="Product not in favorites" {
     *   "message": "Not in favorites"
     * }
     *
     * @response 400 scenario="Error occurred" {
     *   "message": "error occurred during the process of removing from favorites",
     *   "error": {}
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Invalid product ID" {
     *   "message": "The product id field must be a valid UUID.",
     *   "errors": {
     *     "product_id": ["The product id field must be a valid UUID."]
     *   }
     * }
     *
     * @responseField message string Success or error message
     *
     * @group Products
     */
    #[Authenticated]
    public function removeFromFavorites(Request $request, string $product_id)
    {
        $user = auth()->user();
        $validatedData = Validator::make(
            [
                "product_id" => $product_id,
            ],
            [
                "product_id" => "required|UUID",
            ],
        )->validate();

        try {
            if (
                $user
                    ->favoriteProducts()
                    ->where("product_id", $validatedData["product_id"])
                    ->exists()
            ) {
                $user
                    ->favoriteProducts()
                    ->where("product_id", $validatedData["product_id"])
                    ->delete();
                return response()->json([
                    "message" => "Removed from favorites",
                ]);
            } else {
                return response()->json(["message" => "Not in favorites"], 404);
            }
        } catch (\Exception $e) {
            return response()->json(
                [
                    "message" =>
                        "error occurred during the process of removing from favorites",
                    "error" => $e,
                ],
                400,
            );
        }
    }

    /**
     * Get User's Favorite Products
     *
     * Retrieves a paginated list of the authenticated user's favorite products,
     * including product details, variations, and pricing information.
     *
     * @queryParam page integer optional Page number for pagination (minimum: 1). Example: 1
     * @queryParam per_page integer optional Number of items per page (default: 10). Example: 10
     *
     * @response 200 scenario="Successful request" {
     *   "data": [
     *     {
     *       "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "name": "Espresso Coffee",
     *       "category_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "featured": "featured",
     *       "status": "Active",
     *       "category_name": "Hot Beverages",
     *       "is_favorite": true,
     *       "variations": [
     *         {
     *           "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *           "name": "Small",
     *           "emp_price": 250,
     *           "cus_price": 350,
     *           "product_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *           "status": "Active",
     *           "sorting": 1,
     *           "image": "https://example.com/storage/variations/small-espresso.jpg"
     *         }
     *       ]
     *     }
     *   ],
     *   "links": {
     *     "first": "http://example.com/api/favorites?page=1",
     *     "last": "http://example.com/api/favorites?page=3",
     *     "prev": null,
     *     "next": "http://example.com/api/favorites?page=2"
     *   },
     *   "meta": {
     *     "current_page": 1,
     *     "from": 1,
     *     "last_page": 3,
     *     "per_page": 10,
     *     "to": 10,
     *     "total": 25
     *   }
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @responseField data array Array of favorite products
     * @responseField data[].id string UUID of the product
     * @responseField data[].name string Name of the product
     * @responseField data[].category_id string UUID of the product's category
     * @responseField data[].featured string Featured status (featured/new/sale/regular/special)
     * @responseField data[].status string Product status (Active/Inactive)
     * @responseField data[].category_name string Name of the product's category
     * @responseField data[].is_favorite boolean Always true for favorite products
     * @responseField data[].variations array Array of product variations
     * @responseField data[].variations[].id string UUID of the variation
     * @responseField data[].variations[].name string Name of the variation
     * @responseField data[].variations[].emp_price integer Employee price in cents
     * @responseField data[].variations[].cus_price integer Customer price in cents
     * @responseField data[].variations[].status string Variation status (Active/Inactive)
     * @responseField data[].variations[].sorting integer Sort order of the variation
     * @responseField data[].variations[].image string URL to the variation's image
     *
     * @group Products
     */
    #[Authenticated]
    public function getFavorites(Request $request)
    {
        $validated_data = Validator::make(
            [
                "page" => $request["page"],
                "per_page" => $request["per_page"],
            ],
            [
                "page" => "nullable|integer",
                "per_page" => "nullable|integer",
            ],
        )->validate();

        $user = auth()->user();

        $favorites = $user->favoriteProducts;

        $productsQuery = Product::query()->with("variations");
        $productsQuery->whereIn(
            "id",
            $favorites->pluck("product_id")->toArray(),
        );

        $products = $productsQuery
            ->orderBy("status")
            ->orderBy("featured")
            ->orderBy("created_at", "desc")
            ->paginate($validated_data["per_page"] ?? 10)
            ->through(function ($product) {
                return [
                    "id" => $product->id,
                    "name" => $product->name,
                    "category_id" => $product->category_id,
                    "featured" => $product->featured,
                    "status" => $product->status,
                    "category_name" => $product->category->name,
                    "is_favorite" => true,
                    "variations" => $product->variations
                        ->sortBy("status")
                        ->values()
                        ->map(
                            fn($variation) => [
                                "id" => $variation->id,
                                "name" => $variation->name,
                                "emp_price" => $variation->emp_price,
                                "cus_price" => $variation->cus_price,
                                "product_id" => $variation->product_id,
                                "status" => $variation->status,
                                "sorting" => $variation->sorting,
                                "image" => $variation->getFirstMediaUrl(
                                    "variations",
                                ),
                            ],
                        ),
                ];
            });

        return response()->json($products);
    }

    /**
     * Get Product Categories
     *
     * Retrieves all product categories with their product counts and images.
     * Supports optional name filtering to search for specific categories.
     *
     * @queryParam name string optional Filter categories by name (partial match). Example: coffee
     *
     * @response 200 scenario="Successful request" {
     *   "categories": [
     *     {
     *       "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "name": "Hot Beverages",
     *       "status": "Active",
     *       "products_count": 15,
     *       "image": "https://example.com/storage/categories/hot-beverages.jpg"
     *     },
     *     {
     *       "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5h",
     *       "name": "Cold Beverages",
     *       "status": "Active",
     *       "products_count": 8,
     *       "image": "https://example.com/storage/categories/cold-beverages.jpg"
     *     }
     *   ]
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The name must be a string.",
     *   "errors": {
     *     "name": ["The name must be a string."]
     *   }
     * }
     *
     * @responseField categories array Array of product categories
     * @responseField categories[].id string UUID of the category
     * @responseField categories[].name string Name of the category
     * @responseField categories[].status string Category status (Active/Inactive)
     * @responseField categories[].products_count integer Number of products in this category
     * @responseField categories[].image string URL to the category's image
     *
     * @group Products
     */
    #[unauthenticated]
    public function getCategories(Request $request)
    {
        $name = $request->query("name", "");

        $validatedData = Validator::make(
            ["name" => $name],
            ["name" => "string"],
        )->validate();

        $categoriesQuery = Category::withCount("products");

        if (!empty($validatedData["name"])) {
            $categoriesQuery->where(
                "name",
                "LIKE",
                "%" . $validatedData["name"] . "%",
            );
        }

        $categories = $categoriesQuery->get();

        if ($categories) {
            foreach ($categories as $category) {
                $category->setAttribute(
                    "image",
                    $category->getFirstMediaUrl("categories"),
                );
                $category->makeHidden([
                    "media",
                    "products",
                    "created_at",
                    "updated_at",
                ]);
            }
        }

        return response()->json(["categories" => $categories]);
    }

    /**
     * Get Products
     *
     * Retrieves a paginated list of products with filtering options. Products include
     * their variations, pricing, and favorite status for authenticated users.
     *
     * @queryParam search string optional Search products by name or variation name. Example: espresso
     * @queryParam category_id string optional Filter by category UUID. Example: 9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g
     * @queryParam featured string optional Filter by featured status. Must be one of: regular, new, sale, special, featured. Example: featured
     * @queryParam status string optional Filter by product status. Must be one of: Active, Inactive. Example: Active
     * @queryParam page integer optional Page number for pagination (minimum: 1). Example: 1
     * @queryParam per_page integer optional Number of items per page (minimum: 1, default: 10). Example: 10
     *
     * @response 200 scenario="Successful request" {
     *   "data": [
     *     {
     *       "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "name": "Espresso Coffee",
     *       "category_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "featured": "featured",
     *       "status": "Active",
     *       "is_favorite": true,
     *       "category_name": "Hot Beverages",
     *       "variations": [
     *         {
     *           "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *           "name": "Small",
     *           "emp_price": 250,
     *           "cus_price": 350,
     *           "product_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *           "status": "Active",
     *           "sorting": 1,
     *           "image": "https://example.com/storage/variations/small-espresso.jpg"
     *         }
     *       ]
     *     }
     *   ],
     *   "links": {
     *     "first": "http://example.com/api/products?page=1",
     *     "last": "http://example.com/api/products?page=5",
     *     "prev": null,
     *     "next": "http://example.com/api/products?page=2"
     *   },
     *   "meta": {
     *     "current_page": 1,
     *     "from": 1,
     *     "last_page": 5,
     *     "per_page": 10,
     *     "to": 10,
     *     "total": 50
     *   }
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The featured field must be one of: regular, new, sale, special, featured.",
     *   "errors": {
     *     "featured": ["The featured field must be one of: regular, new, sale, special, featured."],
     *     "status": ["The status field must be one of: Active, Inactive."]
     *   }
     * }
     *
     * @responseField data array Array of products
     * @responseField data[].id string UUID of the product
     * @responseField data[].name string Name of the product
     * @responseField data[].category_id string UUID of the product's category
     * @responseField data[].featured string Featured status (featured/new/sale/regular/special)
     * @responseField data[].status string Product status (Active/Inactive)
     * @responseField data[].is_favorite boolean Whether the product is in user's favorites (false if not authenticated)
     * @responseField data[].category_name string Name of the product's category
     * @responseField data[].variations array Array of product variations
     * @responseField data[].variations[].id string UUID of the variation
     * @responseField data[].variations[].name string Name of the variation
     * @responseField data[].variations[].emp_price integer Employee price in cents
     * @responseField data[].variations[].cus_price integer Customer price in cents
     * @responseField data[].variations[].status string Variation status (Active/Inactive)
     * @responseField data[].variations[].sorting integer Sort order of the variation
     * @responseField data[].variations[].image string URL to the variation's image
     *
     * @group Products
     */
    public function getProducts(Request $request)
    {
        $validatedData = Validator::make(
            [
                "search" => $request->search,
                "category_id" => $request->category_id,
                "featured" => $request->featured,
                "status" => $request->status,
                "page" => $request->page,
                "per_page" => $request->per_page,
            ],
            [
                "search" => "nullable|string",
                "category_id" => "nullable|uuid|exists:categories,id",
                "featured" => "nullable|in:regular,new,sale,special,featured",
                "status" => "nullable|in:Active,Inactive",
                "page" => "nullable|integer|min:1",
                "per_page" => "nullable|integer|min:1",
            ],
        )->validate();

        $productsQuery = Product::with("variations");

        if (!empty($validatedData["category_id"])) {
            $productsQuery->where("category_id", $validatedData["category_id"]);
        }

        if (!empty($validatedData["featured"])) {
            $productsQuery->where("featured", $validatedData["featured"]);
        }

        if (isset($validatedData["status"])) {
            $productsQuery->where("status", "=", $validatedData["status"]);
        }

        if (!empty($validatedData["search"])) {
            $productsQuery
                ->where("name", "LIKE", "%" . $validatedData["search"] . "%")
                ->orWhereRelation(
                    "variations",
                    "name",
                    "LIKE",
                    "%{$validatedData["search"]}%",
                );
        }

        // Fetch favorite product IDs for the authenticated user
        $favoriteProductIds = auth("sanctum")
            ->user()
            ?->favoriteProducts()
            ->pluck("product_id")
            ->toArray();

        $products = $productsQuery
            ->orderBy("status")
            ->orderBy("featured")
            ->orderBy("created_at", "desc")
            ->paginate($validatedData["per_page"] ?? 10)
            ->through(function ($product) use ($favoriteProductIds) {
                $fav = false;
                if ($favoriteProductIds) {
                    $fav = in_array($product->id, $favoriteProductIds);
                }
                return [
                    "id" => $product->id,
                    "name" => $product->name,
                    "category_id" => $product->category_id,
                    "featured" => $product->featured,
                    "status" => $product->status,
                    "is_favorite" => $fav,
                    "category_name" => $product->category->name,
                    "variations" => $product->variations
                        ->sortBy("status")
                        ->values()
                        ->map(
                            fn($variation) => [
                                "id" => $variation->id,
                                "name" => $variation->name,
                                "emp_price" => $variation->emp_price,
                                "cus_price" => $variation->cus_price,
                                "product_id" => $variation->product_id,
                                "status" => $variation->status,
                                "sorting" => $variation->sorting,
                                "image" => $variation->getFirstMediaUrl(
                                    "variations",
                                ),
                            ],
                        ),
                ];
            });

        return response()->json($products);
    }

    /**
     * @urlParam product_id string required Product ID
     * @bodyParam status string required Status (Active, Inactive)
     * @return JsonResponse
     * @group Products
     */
    #[Authenticated]
    public function changeProductStatus(Request $request, string $product_id)
    {
        $validatedData = Validator::make(
            [
                "product_id" => $product_id,
                "status" => $request->status,
            ],
            [
                "product_id" => "required|UUID|exists:products,id",
                "status" => "required|in:Active,Inactive",
            ],
        )->validate();

        try {
            $product = Product::find($validatedData["product_id"]);

            $product->status = $validatedData["status"];
            $product->save();

            return response()->json([
                "data" => $product,
                "message" => "Product status updated successfully",
            ]);
        } catch (\Exception $e) {
            return response()->json(
                [
                    "message" =>
                        "An error occurred during the process of changing product status",
                    "error" => $e,
                ],
                400,
            );
        }
    }

    /**
     * @urlParam variation_id string required Variation ID
     * @bodyParam status string required Status (Active, Inactive)
     * @return JsonResponse
     *
     * @group Products
     */
    public function changeProductVariationStatus(
        Request $request,
        string $variation_id,
    ) {
        $validatedData = Validator::make(
            ["variation_id" => $variation_id, "status" => $request->status],
            [
                "variation_id" => "required|UUID|exists:variations,id",
                "status" => "required|in:Active,Inactive",
            ],
        )->validate();
        try {
            $variation = Variation::findOrfail($validatedData["variation_id"]);
            $variation->status = $validatedData["status"];
            $variation->save();

            return response()->json([
                "data" => $variation,
                "message" => "Variation status updated successfully",
            ]);
        } catch (\Exception $e) {
            return response()->json(
                [
                    "message" =>
                        "An error occurred during the process of changing variation status",
                    "error" => $e,
                ],
                400,
            );
        }
    }

    /**
     * @urlParam $category_id string required Category ID
     * @return JsonResponse
     * @group Products
     */
    #[unauthenticated]
    public function getCategory(Request $request, string $category_id)
    {
        $validatedData = Validator::make(
            ["category_id" => $category_id],
            ["category_id" => "required|uuid|exists:categories,id"],
        )->validate();
        $category = Category::findOrfail($validatedData["category_id"]);

        $category->setAttribute(
            "image",
            $category->getFirstMediaUrl("categories"),
        );
        $category->makeHidden(["media", "created_at", "updated_at"]);

        return response()->json($category);
    }

    /**
     * @urlParam $variation_id string required Variation ID
     * @return JsonResponse
     * @group Products
     */
    #[Authenticated]
    public function getVenerationImage(Request $request, string $variation_id)
    {
        $validatedData = Validator::make(
            ["variation_id" => $variation_id],
            ["variation_id" => "required|uuid|exists:variations,id"],
        )->validate();
        $variation = Variation::findOrfail($validatedData["variation_id"]);

        if ($variation->getFirstMediaUrl("variations") == null) {
            return response()->json([], 404);
        }

        $variation->setAttribute(
            "image",
            $variation->getFirstMediaUrl("variations"),
        );
        $variation->makeHidden(["media", "created_at", "updated_at"]);

        return response()->json($variation);
    }
}
