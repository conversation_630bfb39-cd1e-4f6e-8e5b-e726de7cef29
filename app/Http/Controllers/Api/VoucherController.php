<?php

namespace App\Http\Controllers\Api;

use App\Events\BalanceEvent;
use App\Http\Controllers\Controller;
use App\Models\Balance;
use App\Models\Voucher;
use App\Models\VouchersUsers;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Authenticated;

class VoucherController extends Controller
{
    /**
     * Redeem Voucher Code
     *
     * Redeems a voucher code and adds the voucher value to the user's wallet balance.
     * The voucher must be valid, not expired, and not previously redeemed. Upon successful
     * redemption, the voucher value is added to the user's balance and the voucher is marked as used.
     *
     * @bodyParam code string required The voucher code to redeem. Must be between 6-255 characters. Example: SAVE20OFF
     *
     * @response 200 scenario="Voucher redeemed successfully" {
     *   "message": "Voucher redeemed",
     *   "value": "25.00 LYD"
     * }
     *
     * @response 400 scenario="Voucher not found" {
     *   "message": "Voucher not found or redeemed"
     * }
     *
     * @response 400 scenario="Voucher not valid" {
     *   "message": "Voucher not valid. please use a valid code"
     * }
     *
     * @response 400 scenario="Voucher already redeemed" {
     *   "message": "Voucher not found or redeemed"
     * }
     *
     * @response 400 scenario="Voucher expired" {
     *   "message": "Voucher expired"
     * }
     *
     * @response 400 scenario="Redemption failed" {
     *   "message": "Voucher not found or redeemed"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The code field is required.",
     *   "errors": {
     *     "code": ["The code field is required."]
     *   }
     * }
     *
     * @responseField message string Success or error message
     * @responseField value string The redeemed voucher value with currency (only on success)
     *
     * @group Vouchers
     */
    #[Authenticated]
    public function redeem_code(Request $request)
    {
        $request->validate([
            "code" => "required|string|min:6|max:255",
        ]);

        $user = $request->user();

        $voucher = Voucher::where("code", $request->code)->first();

        if (!$voucher) {
            return response()->json(
                [
                    "message" => "Voucher not found or redeemed",
                ],
                400
            );
        }

        if (!$voucher->is_valid) {
            return response()->json(
                [
                    "message" => "Voucher not valid. please use a valid code",
                ],
                400
            );
        }

        if ($voucher->isRedeemed()) {
            return response()->json(
                [
                    "message" => "Voucher not found or redeemed",
                ],
                400
            );
        }

        if ($voucher->isExpired()) {
            return response()->json(
                [
                    "message" => "Voucher expired",
                ],
                400
            );
        }

        $result = \DB::Transaction(function () use ($request, $voucher, $user) {
            try {
                $balance = Balance::create([
                    "user_id" => $request->user()->id,
                    "amount" => $voucher->value,
                    "action_type" => "deposit",
                    "is_voucher" => true,
                ]);

                event(
                    new BalanceEvent($user, $voucher->value / 100, "deposit")
                );

                activity("balance")
                    ->causedBy(\Auth::user())
                    ->performedOn($user)
                    ->withProperties([
                        "amount" => $voucher->value,
                        "type" => "deposit",
                    ])
                    ->log("deposit" . " balance");

                VouchersUsers::create([
                    "voucher_id" => $voucher->id,
                    "user_id" => $user->id,
                    "balance_id" => $balance->id,
                ]);

                return true;
            } catch (\Exception $e) {
                return false;
            }
        });

        if (!$result) {
            return response()->json(
                [
                    "message" => "Voucher not found or redeemed",
                ],
                400
            );
        }

        return response()->json([
            "message" => "Voucher redeemed",
            "value" => $voucher->value / 100 . " LYD",
        ]);
    }
}
