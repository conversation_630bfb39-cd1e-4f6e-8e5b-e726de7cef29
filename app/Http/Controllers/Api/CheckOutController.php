<?php

namespace App\Http\Controllers\Api;

use App\Events\OrdersEvent;
use App\Events\OrdersStatus;
use App\Http\Controllers\Controller;
use App\Models\AttendanceEvent;
use App\Models\Balance;
use App\Models\Invoice;
use App\Models\InvoiceDetails;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\OrderTransaction;
use App\Models\Product;
use App\Models\User;
use App\Models\Variation;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Knuckles\Scribe\Attributes\Authenticated;

class CheckOutController extends Controller
{
    /**
     * Create Order
     *
     * Creates a new order with the specified products and payment method. This endpoint handles
     * the complete order creation process including payment processing, inventory validation,
     * and order tracking. Orders can only be placed when the cafe is open (employees are checked in).
     *
     * @bodyParam payment_method string required The payment method for the order. Must be one of: wallet, subscription, product_subscription, mixed. Example: wallet
     * @bodyParam place_id string required The UUID of the place where the order will be delivered/picked up. Example: 9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g
     * @bodyParam notes string optional Additional notes or special instructions for the order. Example: Extra hot, no sugar
     * @bodyParam products array required Array of products to order. Must contain at least one product.
     * @bodyParam products[].id string required The UUID of the product variation to order. Example: 9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g
     * @bodyParam products[].qty integer required The quantity of the product to order (minimum: 1). Example: 2
     *
     * @response 200 scenario="Order created successfully" {
     *   "data": {
     *     "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *     "user_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *     "invoice_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *     "total": "15.50",
     *     "number": "ORD-2024-001",
     *     "start": "2024-01-15T09:00:00.000000Z",
     *     "end": null,
     *     "place_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *     "status": "pending",
     *     "notes": "Extra hot, no sugar",
     *     "created_at": "2024-01-15T09:00:00.000000Z",
     *     "updated_at": "2024-01-15T09:00:00.000000Z"
     *   },
     *   "message": "order created successfully"
     * }
     *
     * @response 400 scenario="Cafe closed" {
     *   "message": "Order can not be placed, Cafe Closed"
     * }
     *
     * @response 400 scenario="Products unavailable" {
     *   "message": "Some products are not available",
     *   "unavailable_products": [
     *     {
     *       "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "name": "Large Espresso"
     *     }
     *   ]
     * }
     *
     * @response 400 scenario="Insufficient balance" {
     *   "message": "Insufficient balance"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The payment method field is required.",
     *   "errors": {
     *     "payment_method": ["The payment method field is required."],
     *     "place_id": ["The place id field must be a valid UUID."],
     *     "products": ["The products field is required."],
     *     "products.0.id": ["The products.0.id field must be a valid UUID."],
     *     "products.0.qty": ["The products.0.qty field must be at least 1."]
     *   }
     * }
     *
     * @responseField data.id string UUID of the created order
     * @responseField data.user_id string UUID of the user who placed the order
     * @responseField data.invoice_id string UUID of the associated invoice
     * @responseField data.total string Total amount of the order in decimal format
     * @responseField data.number string Unique order number for tracking
     * @responseField data.start string ISO 8601 timestamp when the order was placed
     * @responseField data.end string|null ISO 8601 timestamp when the order was completed (null for pending orders)
     * @responseField data.place_id string UUID of the delivery/pickup place
     * @responseField data.status string Order status (pending, accepted, rejected, completed, cancelled)
     * @responseField data.notes string|null Additional notes for the order
     * @responseField data.created_at string ISO 8601 timestamp of order creation
     * @responseField data.updated_at string ISO 8601 timestamp of last update
     * @responseField message string Success message
     * @responseField unavailable_products array Array of products that are not available (only in error scenarios)
     * @responseField unavailable_products[].id string UUID of the unavailable product
     * @responseField unavailable_products[].name string Name of the unavailable product
     *
     * @group Checkout
     */
    #[Authenticated]
    public function checkouts(Request $request)
    {
        $rulesOfProducts = [
            "*.id" => "required|uuid|exists:variations,id",
            "*.qty" => "required|integer|min:1",
        ];
        $rules = [
            "payment_method" =>
                "required|in:wallet,subscription,product_subscription,mixed",
            "place_id" => "required|uuid|exists:places,id",
            "notes" => "nullable|string",
            "products" => "required|array",
        ];

        $validator = Validator::make(
            $request->only("payment_method", "place_id", "notes", "products"),
            $rules,
        );

        if ($validator->fails()) {
            return response()->json($validator->errors());
        }

        $validatorProducts = Validator::make(
            $request->products,
            $rulesOfProducts,
        );

        if ($validatorProducts->fails()) {
            return response()->json($validatorProducts->errors());
        }
        $validator = $validator->validated();
        $products = $this->margeProductsAndQuantities(
            $validatorProducts->validated(),
        );

        $unavailableProducts = [];

        foreach ($products as $key => $product) {
            if (!$this->validateAvailability($key)) {
                $unavailableProducts[] = [
                    "id" => $key,
                    "name" => Variation::find($key)->name,
                ];
            }
        }

        if (!empty($unavailableProducts)) {
            return response()->json(
                [
                    "message" => "Some products are not available",
                    "unavailable_products" => $unavailableProducts,
                ],
                400,
            );
        }

        $user = auth()->user();

        $payment_method = $validator["payment_method"];
        $place = $validator["place_id"];
        $notes = $validator["notes"] ?? null;
        $total = $this->getTotal($products, $user);
        $balanceOk = false;

        $attendance = \DB::query()
            ->selectRaw("user_id, type")
            ->fromSub(
                "select row_number() over (partition by user_id order by created_at desc) rn, user_id, type from attendance_events",
                "l",
            )
            ->where("rn", 1)
            ->where("type", "checkin")
            ->get();

        if ($attendance->isEmpty()) {
            return response()->json(
                [
                    "message" => "Order can not be placed, Cafe Closed",
                ],
                400,
            );
        }

        if ($payment_method == "wallet" || $payment_method == "subscription") {
            $balanceOk = $this->checkBalanceInWalletAndSubscription(
                $total,
                $payment_method,
                $user,
            );
        } elseif ($payment_method == "product_subscription") {
            foreach ($products as $product => $qty) {
                $balanceOk = $this->checkBalanceInProductSubscription(
                    $qty,
                    $product,
                    $user,
                );
                if (!$balanceOk) {
                    break;
                }
            }
        } elseif ($payment_method == "mixed") {
            $balanceOk = $this->checkBalanceMixed($total, $user);
        }

        if (!$balanceOk) {
            return response()
                ->json([
                    "message" => "Insufficient balance",
                ])
                ->setStatusCode(400);
        }

        $order = \DB::Transaction(function () use (
            $products,
            $user,
            $total,
            $payment_method,
            $place,
            $notes,
        ) {
            $invoice = Invoice::create([
                "user_id" => $user->id,
                "total" => $total,
                "payment_method" => $payment_method,
                "status" => "default",
            ]);

            $pro = [];
            foreach ($products as $product => $qty) {
                $pro[] = $product;
            }

            $price_type =
                $user->is_employee == "Yes" ? "emp_price" : "cus_price";
            $variations = Variation::whereIn("id", $pro)->get();
            $invoiceDetails = $variations->map(function ($variation) use (
                $products,
                $invoice,
                $price_type,
            ) {
                return [
                    "variant_id" => $variation->id,
                    "quantity" => $products[$variation->id],
                    "price" => $variation->$price_type,
                ];
            });
            $invoice->invoiceDetails()->createMany($invoiceDetails->toArray());

            $order = Order::create([
                "user_id" => $user->id,
                "invoice_id" => $invoice->id,
                "status" => "pending",
                "total" => $total,
                "start" => now(),
                "place_id" => $place,
                "notes" => $notes,
            ]);

            $orderDetails = $variations->map(function ($variation) use (
                $products,
                $order,
                $price_type,
            ) {
                return [
                    "variant_id" => $variation->id,
                    "quantity" => $products[$variation->id],
                    "price" => $variation->$price_type,
                    "refunded" => "No",
                ];
            });
            $order->order_details()->createMany($orderDetails->toArray());

            if ($payment_method == "wallet") {
                Balance::create([
                    "user_id" => $user->id,
                    "amount" => -$total * 100,
                    "action_type" => "charge",
                    "invoice_id" => $invoice->id,
                ]);
            }

            if ($payment_method == "subscription") {
                $features = $user->subscription->plan
                    ->features()
                    ->get()
                    ->where("variation_id", null)
                    ->first()->name;
                $user->consume($features, $total * 100);
            }

            if ($payment_method == "product_subscription") {
                foreach ($products as $product => $qty) {
                    $user->consume(Variation::find($product)->name, $qty * 100);
                }
            }

            if ($payment_method == "mixed") {
                $features = $user->subscription->plan
                    ->features()
                    ->where("variation_id", null)
                    ->first()->name;
                $subscription_balance = $user->balance($features);
                //                throw new \Exception($user->balance($features));

                // consume subscription balance
                $user->consume($features, $subscription_balance);
                $balance_charge = ($total - $subscription_balance / 100) * 100;
                //                throw new \Exception($balance_charge);

                // consume wallet balance
                Balance::create([
                    "user_id" => $user->id,
                    "amount" => -$balance_charge,
                    "action_type" => "charge",
                    "invoice_id" => $invoice->id,
                ]);
            }

            $this->orderTransaction($order, "pending", auth()->user(), $notes);

            event(new OrdersEvent($order, "new-order"));
            event(new OrdersStatus($user, $order));

            return $order;
        });

        return response()->json([
            "data" => $order,
            "message" => "order created successfully",
        ]);
    }

    /**
     * Check Product Subscription Balance
     *
     * Validates whether the authenticated user has sufficient product subscription balance
     * to purchase the specified products. This is used for product-specific subscriptions
     * where users have allocated quantities for specific items.
     *
     * @bodyParam products array required Array of products to check subscription balance for. Must contain at least one product.
     * @bodyParam products[].id string required The UUID of the product variation to check. Must exist in the variations table. Example: 9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g
     * @bodyParam products[].qty integer required The quantity to check (minimum: 1). Example: 3
     *
     * @response 200 scenario="Sufficient balance" {
     *   "message": "balance ok"
     * }
     *
     * @response 406 scenario="Insufficient balance" {
     *   "message": "Insufficient balance"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The products field is required.",
     *   "errors": {
     *     "products": ["The products field is required."],
     *     "products.0.id": ["The products.0.id field must be a valid UUID."],
     *     "products.0.qty": ["The products.0.qty field must be at least 1."]
     *   }
     * }
     *
     * @responseField message string Success or error message indicating balance status
     *
     * @group Checkout
     */
    #[Authenticated]
    public function checkProductSubscriptionBalance(Request $request)
    {
        $rulesOfProducts = [
            "products" => "required|array|min:1",
            "products.*.id" => "required|uuid|exists:variations,id",
            "products.*.qty" => "required|integer|min:1",
        ];
        $validatorProducts = Validator::make($request->all(), $rulesOfProducts);

        if ($validatorProducts->fails()) {
            return response()->json($validatorProducts->errors());
        }
        $balanceOk = false;
        $validated = $validatorProducts->validated();
        $products = $this->margeProductsAndQuantities($validated["products"]);
        $user = auth()->user();

        foreach ($products as $product => $qty) {
            $balanceOk = $this->checkBalanceInProductSubscription(
                $qty,
                $product,
                $user,
            );
            if (!$balanceOk) {
                break;
            }
        }

        if (!$balanceOk) {
            return response()->json(
                [
                    "message" => "Insufficient balance",
                ],
                406,
            );
        }

        return response()->json([
            "message" => "balance ok",
        ]);
    }

    /**
     * Check Wallet Balance Before Checkout
     *
     * Validates whether the authenticated user has sufficient wallet or subscription balance
     * to complete a purchase with the specified total amount. This is used to pre-validate
     * payment capability before proceeding with order creation.
     *
     * @bodyParam payment_method string required The payment method to check. Must be one of: wallet, subscription, mixed. Example: wallet
     * @bodyParam total number required The total amount to check against the user's balance. Example: 15.50
     *
     * @response 200 scenario="Sufficient balance" {
     *   "message": "balance ok"
     * }
     *
     * @response 400 scenario="Insufficient balance" {
     *   "message": "Insufficient balance"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The payment method field is required.",
     *   "errors": {
     *     "payment_method": ["The payment method field is required."],
     *     "total": ["The total field is required."]
     *   }
     * }
     *
     * @responseField message string Success or error message indicating balance status
     *
     * @group Checkout
     */
    #[Authenticated]
    public function checkWalletBalanceBeforeCheckout(Request $request)
    {
        $rules = [
            "payment_method" => "required|in:wallet,subscription,mixed",
            "total" => "required|numeric",
        ];

        $validator = Validator::make(
            $request->only("payment_method", "total"),
            $rules,
        );

        if ($validator->fails()) {
            return response()->json($validator->errors());
        }

        $validator = $validator->validated();
        $user = auth()->user();
        $balanceOk = false;
        if (
            $validator["payment_method"] == "wallet" ||
            $validator["payment_method"] == "subscription"
        ) {
            $balanceOk = $this->checkBalanceInWalletAndSubscription(
                $validator["total"],
                $validator["payment_method"],
                $user,
            );
        }

        if ($validator["payment_method"] == "mixed") {
            $balanceOk = $this->checkBalanceMixed($validator["total"], $user);
        }

        if (!$balanceOk) {
            return response()->json(
                [
                    "message" => "Insufficient balance",
                ],
                400,
            );
        }
        return response()->json([
            "message" => "balance ok",
        ]);
    }

    /**
     * Get Order Details
     *
     * Retrieves detailed information about a specific order including order items,
     * pricing, quantities, refunds, and associated metadata. This endpoint provides
     * comprehensive order information for order management and customer service.
     *
     * @urlParam order_id string required The UUID of the order to retrieve details for. Must exist in the orders table. Example: 9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g
     *
     * @response 200 scenario="Order details retrieved successfully" {
     *   "order": {
     *     "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *     "user_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *     "invoice_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *     "total": "15.50",
     *     "number": "ORD-2024-001",
     *     "start": "2024-01-15T09:00:00.000000Z",
     *     "end": "2024-01-15T09:30:00.000000Z",
     *     "place_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *     "status": "completed",
     *     "notes": "Extra hot, no sugar",
     *     "payment_method": "wallet",
     *     "invoice_number": "INV-2024-001",
     *     "place_name": "Main Counter"
     *   },
     *   "order_details": [
     *     {
     *       "order_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "variant_id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *       "price": "5.50",
     *       "ordered": 2,
     *       "refunded": 0,
     *       "variant_name": "Large Espresso",
     *       "img": "https://example.com/storage/variations/large-espresso.jpg"
     *     }
     *   ]
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 404 scenario="Order not found" {
     *   "message": "No query results for model [App\\Models\\Order]."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The order id field must be a valid UUID.",
     *   "errors": {
     *     "order_id": ["The order id field must be a valid UUID."]
     *   }
     * }
     *
     * @responseField order.id string UUID of the order
     * @responseField order.user_id string UUID of the user who placed the order
     * @responseField order.invoice_id string UUID of the associated invoice
     * @responseField order.total string Total amount of the order
     * @responseField order.number string Unique order number for tracking
     * @responseField order.start string ISO 8601 timestamp when the order was placed
     * @responseField order.end string|null ISO 8601 timestamp when the order was completed
     * @responseField order.place_id string UUID of the delivery/pickup place
     * @responseField order.status string Order status (pending, accepted, rejected, completed, cancelled)
     * @responseField order.notes string|null Additional notes for the order
     * @responseField order.payment_method string Payment method used (wallet, subscription, product_subscription, mixed)
     * @responseField order.invoice_number string Invoice number for accounting
     * @responseField order.place_name string Name of the delivery/pickup place
     * @responseField order_details array Array of order items
     * @responseField order_details[].order_id string UUID of the order
     * @responseField order_details[].variant_id string UUID of the product variation
     * @responseField order_details[].price string Price per unit of the item
     * @responseField order_details[].ordered integer Total quantity ordered
     * @responseField order_details[].refunded integer Quantity that was refunded
     * @responseField order_details[].variant_name string Name of the product variation
     * @responseField order_details[].img string URL to the product variation image
     *
     * @group Checkout
     */
    #[Authenticated]
    public function getOrderDetails(Request $request, string $order_id)
    {
        $validator = Validator::make(
            ["order_id" => $order_id],
            [
                "order_id" => "required|uuid|exists:orders,id",
            ],
        )->validated();

        $order = Order::findOrFail($validator["order_id"]);
        $order->setAttribute("payment_method", $order->invoice->payment_method);
        $order->setAttribute("invoice_number", $order->invoice->number);
        $order->setAttribute("place_name", $order->place->name);
        $order->makeHidden(["created_at", "updated_at", "invoice", "place"]);

        $orderDetails = $order
            ->order_details()
            ->select(
                DB::raw(
                    "order_id,variant_id,max(price) as price, max(quantity) AS ordered,max(quantity)-min(quantity) as refunded",
                ),
            )
            ->groupBy(["variant_id", "order_id"])
            ->get();

        $orderDetails->each(function ($orderDetail) {
            $orderDetail->setAttribute(
                "variant_name",
                $orderDetail->variation->name,
            );
            $orderDetail->setAttribute(
                "img",
                $orderDetail->variation->getFirstMediaUrl("variations"),
            );

            $orderDetail->makeHidden(["variation"]);
        });
        return response()->json([
            "order" => $order,
            "order_details" => $orderDetails,
        ]);
    }

    /**
     * @bodyParam status string required The order status.
     * @urlParam order_id string required The order_id.
     * @return \Illuminate\Http\JsonResponse
     *
     * @group Checkout
     */
    #[Authenticated]
    public function changeOrderStatus(Request $request, string $order_id)
    {
        $validator = Validator::make(
            ["order_id" => $order_id, "status" => $request->status],
            [
                "status" => "required|in:pending,accepted,rejected,completed",
                "order_id" => "required|uuid|exists:orders,id",
            ],
        )->validate();

        $order = Order::findOrFail($validator["order_id"]);
        $attend = AttendanceEvent::latest()
            ->where("user_id", auth()->user()->id)
            ->first();

        if (
            $validator["status"] == "accepted" &&
            $attend &&
            $attend->type == "checkin"
        ) {
            if (
                !$this->orderTransaction($order, "accepted", auth()->user(), "")
            ) {
                return response()->json(
                    [
                        "message" => "order status can't be updated.",
                    ],
                    400,
                );
            }
        }

        if ($validator["status"] == "completed") {
            if (
                !$this->orderTransaction(
                    $order,
                    "completed",
                    auth()->user(),
                    "",
                )
            ) {
                return response()->json(
                    [
                        "message" => "order status can't be updated.",
                    ],
                    400,
                );
            }
            $order->end = now();
        }
        $order->status = $validator["status"];
        $order->save();

        $customer = User::findOrFail($order->user_id);
        event(new OrdersEvent($order, "updated-order"));
        event(new OrdersStatus($customer, $order));

        return response()->json([
            "data" => $order,
            "message" => "order status updated",
        ]);
    }

    /**
     * @queryParam page integer optional min :1
     * @queryParam per_page integer optional min :1
     * @queryParam from date optional
     * @queryParam to date optional
     * @return \Illuminate\Http\JsonResponse
     * @group Checkout
     */
    #[Authenticated]
    public function orderHistory(Request $request)
    {
        $validatedData = Validator::make(
            [
                "page" => $request->page,
                "per_page" => $request->per_page,
                "from" => $request->from,
                "to" => $request->to,
            ],
            [
                "page" => "nullable|integer|min:1",
                "per_page" => "nullable|integer|min:1",
                "from" => "nullable|date",
                "to" => "nullable|date",
            ],
        )->validate();

        $ordersQuery = Order::where("user_id", auth()->user()->id)->orderBy(
            "created_at",
            "desc",
        );

        if (isset($validatedData["from"])) {
            $ordersQuery->whereDate("created_at", ">=", $validatedData["from"]);
        }

        if (isset($validatedData["to"])) {
            $ordersQuery->whereDate("created_at", "<=", $validatedData["to"]);
        }

        $orders = $ordersQuery->paginate($validatedData["per_page"] ?? 10);

        $orders->each(function ($order) {
            $order->setAttribute(
                "payment_method",
                $order->invoice->payment_method,
            );
            $order->makeHidden(["invoice"]);
        });

        return response()->json($orders);
    }

    /**
     * @queryParam place_name string optional
     * @queryParam user_name string optional
     * @queryParam from date optional
     * @queryParam to date optional
     * @queryParam per_page integer optional
     * @queryParam status array required in :[pending, accepted, rejected, completed,cancelled] optional
     * Example ?status[]=pending&status[]=cancelled
     * @queryParam sort string optional (values: asc, desc, default: desc)
     * @queryParam cafe_id uuid optional
     * @return \Illuminate\Http\JsonResponse
     *
     * @group Checkout
     */
    #[Authenticated]
    public function orders(Request $request)
    {
        $validatedData = Validator::make(
            [
                "page" => $request->page,
                "per_page" => $request->per_page,
                "place_name" => $request->place_name,
                "user_name" => $request->user_name,
                "from" => $request->from,
                "to" => $request->to,
                "status" => $request->status,
                "sort" => $request->sort ?? "desc",
                "cafe_id" => $request->cafe_id,
            ],
            [
                "page" => "nullable|integer|min:1",
                "per_page" => "nullable|integer|min:1",
                "place_name" => "nullable|string",
                "user_name" => "nullable|string",
                "from" => "nullable|date",
                "to" => "nullable|date",
                "status" => "nullable|array",
                "status.*" =>
                    "nullable|in:pending,accepted,rejected,completed,cancelled",
                "sort" => "nullable|string|in:asc,desc",
                "cafe_id" => "nullable|uuid",
            ],
        )->validate();

        $ordersQuery =
            $request->sort == "desc"
                ? Order::query()->orderByDesc("created_at")
                : Order::query()->orderBy("created_at");

        if ($validatedData["status"]) {
            $ordersQuery->whereIn("status", $validatedData["status"]);
        } else {
            $ordersQuery->whereIn("status", ["pending", "accepted"]);
        }

        if ($validatedData["cafe_id"]) {
            $cafeId = $validatedData["cafe_id"];
            $ordersQuery->whereHas("place", function ($query) use ($cafeId) {
                $query->where("cafe_id", $cafeId);
            });
        }

        if ($validatedData["place_name"]) {
            $ordersQuery->whereRelation(
                "place",
                "name",
                "like",
                "%{$validatedData["place_name"]}%",
            );
        }

        if ($validatedData["user_name"]) {
            $ordersQuery->whereRelation(
                "user",
                "name",
                "like",
                "%{$validatedData["user_name"]}%",
            );
        }
        if (isset($validatedData["from"])) {
            $ordersQuery->whereDate("created_at", ">=", $validatedData["from"]);
        }

        if (isset($validatedData["to"])) {
            $ordersQuery->whereDate("created_at", "<=", $validatedData["to"]);
        }

        $ordersQuery->orderBy("created_at", "asc");
        $orders = $ordersQuery->paginate($validatedData["per_page"] ?? 10);

        $orders->each(function ($order) {
            $order->setAttribute(
                "payment_method",
                $order->invoice->payment_method,
            );
            $order->setAttribute("place_name", $order->place->name);
            $order->setAttribute("invoice_number", $order->invoice->number);
            $order->setAttribute("user_name", $order->user->name);
            $order->setAttribute(
                "order_detail_count",
                $order->order_details()->count(DB::raw("distinct variant_id")),
            );

            $order->makeHidden(["invoice", "place"]);
            $order->makeHidden(["user"]);
            $order->makeHidden(["order_details"]);
            $order->makeHidden(["created_at"]);
        });

        return response()->json($orders);
    }

    /**
     * @urlParam order_id uuid required The order id.
     * @bodyParam note string optional
     * @return \Illuminate\Http\JsonResponse
     *
     * @group Checkout
     */
    #[Authenticated]
    public function fullRefund(Request $request)
    {
        $validatedData = Validator::make(
            [
                "note" => $request->note,
                "order_id" => $request->order_id,
            ],
            [
                "note" => "nullable|string",
                "order_id" => "required|uuid|exists:orders,id",
            ],
        )->validate();

        $order = Order::findOrFail($validatedData["order_id"]);
        $customer = User::findOrFail($order->user_id);

        if ($order->status == "rejected" or $order->status == "cancelled") {
            return response()->json(
                ["message" => "order can not be refunded"],
                400,
            );
        }

        if (!auth()->user()->can("attendance-create")) {
            if (auth()->user()->id != $customer->id) {
                return response()->json(
                    [
                        "message" =>
                            "you are not authorized to refund this order",
                    ],
                    400,
                );
            }
        }

        if (
            $order->status == "accepted" and
            auth()->user()->id == $customer->id and
            !$customer->can("attendance-create")
        ) {
            return response()->json(
                [
                    "message" =>
                        "you can not refund order to yourself in this stage.",
                ],
                400,
            );
        }

        $invoice = $order->invoice;
        $note = $validatedData["note"] ?? "";

        $transactionResult = DB::Transaction(function () use (
            &$invoice,
            &$order,
            $customer,
            $note,
        ) {
            try {
                if ($invoice->payment_method == "wallet") {
                    $this->refundByWallet($invoice, $order, $customer);
                    if (
                        !$this->orderTransaction(
                            $order,
                            "refund",
                            auth()->user(),
                            $note,
                        )
                    ) {
                        return false;
                    }
                } elseif (
                    $invoice->payment_method == "subscription" &&
                    $customer->subscription != null
                ) {
                    $this->refundBySubscription($invoice, $order, $customer);
                    if (
                        !$this->orderTransaction(
                            $order,
                            "refund",
                            auth()->user(),
                            $note,
                        )
                    ) {
                        return false;
                    }
                } elseif (
                    $invoice->payment_method == "product_subscription" &&
                    $customer->subscription != null
                ) {
                    $this->refundByProductSubscription(
                        $invoice,
                        $order,
                        $customer,
                    );
                    if (
                        !$this->orderTransaction(
                            $order,
                            "refund",
                            auth()->user(),
                            $note,
                        )
                    ) {
                        return false;
                    }
                } elseif (
                    $invoice->payment_method == "mixed" &&
                    $customer->subscription != null
                ) {
                    $this->refundByMixed($invoice, $order, $customer);
                    if (
                        !$this->orderTransaction(
                            $order,
                            "refund",
                            auth()->user(),
                            $note,
                        )
                    ) {
                        return false;
                    }
                }
                return true;
            } catch (\Exception $exception) {
                return false;
            }
        });

        if ($transactionResult === true) {
            event(new OrdersEvent($order, "updated-order"));
            event(new OrdersStatus($customer, $order));
            return response()->json([
                "data" => $order,
                "message" => "successfully refunded",
            ]);
        } else {
            return response()->json(["message" => "failed to refund"], 400);
        }
    }

    /**
     * @bodyParam order_id uuid required The order id.
     * @bodyParam variant_id uuid required The variant id.
     * @bodyParam qty integer required The quantity.
     * @bodyParam note string optional
     * @respons \Illuminate\Http\JsonResponse
     *
     * @group Checkout
     */
    #[Authenticated]
    public function partialRefund(Request $request)
    {
        $validatedData = $request->validate([
            "order_id" => "required|uuid|exists:orders,id",
            "variant_id" => "required|uuid|exists:variations,id",
            "qty" => "required|integer|min:1",
            "note" => "nullable|string",
        ]);

        $order = Order::findOrFail($validatedData["order_id"]);
        $item = $order->order_details
            ->where("variant_id", $validatedData["variant_id"])
            ->where("refunded", "!=", "yes")
            ->first();

        if (!$item) {
            return response()->json(
                ["message" => "Item not found or refunded"],
                400,
            );
        }

        if ($validatedData["qty"] > $item->quantity) {
            return response()->json(
                ["message" => "Quantity is more than you have ordered"],
                400,
            );
        }

        $user = auth()->user();
        $customer = User::findOrFail($order->user_id);
        $invoice = $order->invoice;

        if ($order->status == "rejected" or $order->status == "cancelled") {
            return response()->json(
                ["message" => "Order can not be refunded"],
                400,
            );
        }

        if (!$user->can("attendance-create")) {
            if ($user->id != $customer->id) {
                return response()->json(
                    [
                        "message" =>
                            "you are not authorized to refund this order",
                    ],
                    400,
                );
            }
        }

        if (
            $order->status == "accepted" and
            $user->id == $customer->id and
            !$customer->can("attendance-create")
        ) {
            return response()->json(
                [
                    "message" =>
                        "you can not refund order to yourself in this stage.",
                ],
                400,
            );
        }

        $qty = $item->quantity - $validatedData["qty"];
        $price = $item->price;

        $transactionResult = DB::Transaction(function () use (
            $qty,
            $price,
            &$order,
            &$invoice,
            $user,
            $customer,
            &$item,
            $validatedData,
        ) {
            if ($invoice->payment_method == "wallet") {
                $item->refunded = "yes";
                $item->save();

                OrderDetail::create([
                    "order_id" => $order->id,
                    "variant_id" => $validatedData["variant_id"],
                    "quantity" => $qty,
                    "price" => $price,
                ]);

                return $this->partialRefundWallet(
                    $user,
                    $customer,
                    $order,
                    $invoice,
                    $price,
                    $validatedData,
                );
            }

            if ($invoice->payment_method == "subscription") {
                $item->refunded = "yes";
                $item->save();

                OrderDetail::create([
                    "order_id" => $order->id,
                    "variant_id" => $validatedData["variant_id"],
                    "quantity" => $qty,
                    "price" => $price,
                ]);

                return $this->partialRefundSubscription(
                    $user,
                    $customer,
                    $order,
                    $invoice,
                    $price,
                    $validatedData,
                );
            }

            if ($invoice->payment_method == "product_subscription") {
                $item->refunded = "yes";
                $item->save();

                OrderDetail::create([
                    "order_id" => $order->id,
                    "variant_id" => $validatedData["variant_id"],
                    "quantity" => $qty,
                    "price" => $price,
                ]);

                return $this->partialRefundProductSubscription(
                    $user,
                    $customer,
                    $order,
                    $invoice,
                    $validatedData,
                );
            }

            if ($invoice->payment_method == "mixed") {
                $item->refunded = "yes";
                $item->save();

                OrderDetail::create([
                    "order_id" => $order->id,
                    "variant_id" => $validatedData["variant_id"],
                    "quantity" => $qty,
                    "price" => $price,
                ]);

                return $this->partialRefundMixed(
                    $user,
                    $customer,
                    $order,
                    $invoice,
                    $price,
                    $validatedData,
                );
            }

            return false;
        });

        if (!$transactionResult) {
            return response()->json(["message" => "failed to refund"], 400);
        }
        event(new OrdersEvent($order, "updated-order"));
        event(new OrdersStatus($customer, $order));

        $orderDetails = $order
            ->order_details()
            ->select(
                DB::raw(
                    "order_id,variant_id,max(price) as price, max(quantity) AS ordered,max(quantity)-min(quantity) as refunded",
                ),
            )
            ->groupBy(["variant_id", "order_id"])
            ->get();

        return response()->json([
            "data" => [
                "order" => $order,
                "order_details" => $orderDetails,
            ],
            "message" => "successfully refunded",
        ]);
    }

    private function partialRefundMixed(
        $user,
        $customer,
        $order,
        $invoice,
        $price,
        $validatedData,
    ): bool {
        if ($customer->subscription == null) {
            return false;
        }

        $features = $customer->subscription->plan
            ->features()
            ->where("variation_id", null)
            ->first()->name;
        $expired_at = $customer->subscription->expired_at;

        if ($order->updated_at > $expired_at) {
            return false;
        }
        $balance = Balance::where("invoice_id", $invoice->id)->sum("amount");
        //                throw new \Exception($balance);
        $refund_total = $validatedData["qty"] * $price;
        $balance_amount = ($balance * -1) / 100;

        //        throw new \Exception($refund_total);

        if ($balance_amount > $refund_total) {
            Balance::create([
                "user_id" => $customer->id,
                "invoice_id" => $invoice->id,
                "amount" => $refund_total * 100,
                "action_type" => "refund",
            ]);
        } else {
            // refund the wallet balance and calculate the remaining to refund from subscription

            Balance::create([
                "user_id" => $customer->id,
                "invoice_id" => $invoice->id,
                "amount" => $balance_amount * 100,
                "action_type" => "refund",
            ]);

            $remaining_refund = ($refund_total - $balance_amount) * 100;

            // refund the subscription if  remaining ref    und is greater than 0
            if ($remaining_refund > 0) {
                $customer->consume($features, $remaining_refund * -1);

                // change the payment method to subscription if the all balance is refunded from wallet
                $invoice->payment_method = "subscription";
                $invoice->save();
            }
        }
        $this->repeated_op($order, $invoice, $user, $validatedData);

        return true;
    }

    private function repeated_op($order, $invoice, $user, $validatedData): void
    {
        $order->refresh();

        $sum_qty = $order
            ->order_details()
            ->where("refunded", "!=", "yes")
            ->sum("quantity");

        $order_details = $order
            ->order_details()
            ->where("refunded", "!=", "yes")
            ->get();

        $order->total = $order->orderTotal();
        $order->save();
        if ($sum_qty == 0) {
            $order->status = "cancelled";
            $order->save();
            $invoice->status = "refund";
            $invoice->save();
            $this->orderTransaction(
                $order,
                "full refund",
                $user,
                "this order has been fully refunded",
            );
        } else {
            $invoice->invoiceDetails()->delete();
            foreach ($order_details as $order_detail) {
                InvoiceDetails::create([
                    "invoice_id" => $invoice->id,
                    "variant_id" => $order_detail->variant_id,
                    "quantity" => $order_detail->quantity,
                    "price" => $order_detail->price,
                ]);
            }
            $invoice->total = $invoice->invoiceTotal();
            $invoice->save();
            $this->orderTransaction(
                $order,
                "partial refund",
                $user,
                $validatedData["note"] ?? "",
            );
        }
    }

    private function partialRefundProductSubscription(
        $user,
        $customer,
        $order,
        $invoice,
        $validatedData,
    ): bool {
        if ($customer->subscription == null) {
            return false;
        }

        $feature = $customer->subscription->plan
            ->features()
            ->where("variation_id", $validatedData["variant_id"])
            ->first()->name;
        $customer->consume($feature, $validatedData["qty"] * -100);

        $this->repeated_op($order, $invoice, $user, $validatedData);

        return true;
    }

    private function partialRefundSubscription(
        $user,
        $customer,
        $order,
        $invoice,
        $price,
        $validatedData,
    ): bool {
        $features = $customer->subscription->plan
            ->features()
            ->get()
            ->where("variation_id", null)
            ->first()->name;
        $customer->consume(
            $features,
            $price * $validatedData["qty"] * 100 * -1,
        );
        $this->repeated_op($order, $invoice, $user, $validatedData);

        return true;
    }

    private function partialRefundWallet(
        $user,
        $customer,
        $order,
        $invoice,
        $price,
        $validatedData,
    ): bool {
        Balance::create([
            "user_id" => $customer->id,
            "amount" => $price * $validatedData["qty"] * 100,
            "invoice_id" => $invoice->id,
            "action_type" => "refund",
        ]);
        $this->repeated_op($order, $invoice, $user, $validatedData);

        return true;
    }

    private function orderTransaction(
        $order,
        string $action,
        ?User $user,
        ?string $note,
    ): bool {
        $order_transaction = new OrderTransaction();
        $order_transaction->order_id = $order->id;
        $order_transaction->action = $action;
        $order_transaction->user_id = $user->id ?? null;
        $order_transaction->note = $note ?? "";

        if ($order_transaction->save()) {
            return true;
        } else {
            return false;
        }
    }

    private function refundByWallet($invoice, $order, $user): void
    {
        $invoice->status = "refund";
        $order->status = "cancelled";
        $order->save();
        $invoice->save();
        $total = $invoice->invoiceTotal();
        Balance::create([
            "user_id" => $user->id,
            "amount" => $total * 100,
            "invoice_id" => $invoice->id,
            "action_type" => "refund",
        ]);
    }

    private function refundBySubscription($invoice, $order, $user): void
    {
        $invoice->status = "refund";
        $order->status = "cancelled";
        $order->save();
        $invoice->save();
        $total = $invoice->invoiceTotal();
        $features = $user->subscription->plan
            ->features()
            ->get()
            ->where("variation_id", null)
            ->first()->name;
        $user->consume($features, -$total * 100);
    }

    private function refundByProductSubscription($invoice, $order, $user)
    {
        $invoice->status = "refund";
        $order->status = "cancelled";
        $order->save();
        $invoice->save();

        $invoice->invoiceDetails->each(function ($item) use ($user) {
            $user->consume($item->variant->name, $item->quantity * -100);
        });
    }

    private function refundByMixed($invoice, $order, $user): void
    {
        $invoice->status = "refund";
        $order->status = "cancelled";
        $order->save();
        $invoice->save();
        $total = $invoice->invoiceTotal();

        // calculate subscription balance return
        $balance = Balance::where("invoice_id", "=", $invoice->id)
            ->where("action_type", "charge")
            ->first();

        $subscription_balance_return = $total - ($balance->amount * -1) / 100;

        $features = $user->subscription->plan
            ->features()
            ->get()
            ->where("variation_id", null)
            ->first()->name;

        // refund subscription balance
        $user->consume($features, -$subscription_balance_return * 100);

        // refund wallet balance
        Balance::create([
            "user_id" => $user->id,
            "amount" => $balance->amount * -1,
            "action_type" => "refund",
            "invoice_id" => $invoice->id,
        ]);
    }

    private function getTotal($products, $user): float
    {
        // !TODO: fix map variation and prices
        $productIdList = array_keys($products);
        $variations = Variation::whereIn("id", $productIdList)->get();
        $total = 0;
        $priceType = $user->is_employee === "Yes" ? "emp_price" : "cus_price";

        // Calculate the total price.
        foreach ($variations as $variation) {
            $productPrice = $variation->{$priceType};
            $quantity = $products[$variation->id];
            $productTotal = $productPrice * $quantity;
            $total += $productTotal;
        }

        return $total;
    }

    private function checkBalanceInWalletAndSubscription(
        $total,
        $payment_method,
        $user,
    ): bool {
        if ($payment_method == "wallet") {
            $balance = $user->current_balance() / 100;
            if ($balance < $total) {
                return false;
            } else {
                return true;
            }
        } elseif (
            $payment_method == "subscription" &&
            $user->subscription != null
        ) {
            $features =
                $user->subscription->plan
                    ->features()
                    ->get()
                    ->where("variation_id", null)
                    ->first()->name ?? null;

            if ($features == null) {
                return false;
            }

            $balance = $user->balance($features) / 100;

            if ($balance < $total) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    private function checkBalanceInProductSubscription(
        $quantity,
        $variation_id,
        $user,
    ): bool {
        if (
            $user->canConsume(
                Variation::find($variation_id)->name,
                $quantity * 100,
            )
        ) {
            return true;
        } else {
            return false;
        }
    }

    private function checkBalanceMixed($total, $user): bool
    {
        $wallet = $user->current_balance() / 100;

        if ($user->subscription == null) {
            return false;
        }

        $features =
            $user->subscription->plan
                ->features()
                ->get()
                ->where("variation_id", null)
                ->first()->name ?? null;

        if ($features == null) {
            return false;
        }

        $subscription = $user->balance($features) / 100;

        if ($subscription >= $total) {
            return false;
        }

        $balance = $wallet + $subscription;

        if ($balance < $total) {
            return false;
        } else {
            return true;
        }
    }

    private function margeProductsAndQuantities(array $products): array
    {
        $products = collect($products);
        $products = $products->groupBy("id");

        $products = $products->map(function ($item) {
            return $item->sum("qty");
        });
        //        dd($products);
        return $products->toArray();
    }

    private function validateAvailability($id): bool
    {
        $variation = Variation::find($id);

        if (
            $variation->product->status == "Active" &&
            $variation->status == "Active"
        ) {
            return true;
        } else {
            return false;
        }
    }
}
