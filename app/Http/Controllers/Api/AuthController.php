<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Login;
use App\Http\Requests\Register;
use App\Models\AttendanceEvent;
use App\Models\DeviceTokens;
use App\Models\User;
use Carbon\Carbon;
use DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Knuckles\Scribe\Attributes\Authenticated;
use Knuckles\Scribe\Attributes\Unauthenticated;

class AuthController extends Controller
{
    /**
     * Register New User
     *
     * Creates a new user account and returns an access token for immediate authentication.
     * The user will be created with "Active" status and can immediately use the system.
     *
     * @bodyParam name string required The full name of the user. Must be a string. Example: John <PERSON>
     * @bodyParam email string required The email address of the user. Must be unique and valid email format. Example: <EMAIL>
     * @bodyParam phone string required The phone number of the user. Example: +**********
     * @bodyParam password string required The password for the user account. Must meet security requirements. Example: securepassword123
     *
     * @response 200 scenario="Successful registration" {
     *   "access_token": "1|abcdef123456789...",
     *   "token_type": "Bearer"
     * }
     *
     * @response 401 scenario="Registration failed" {
     *   "message": "Invalid login details"
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The email has already been taken.",
     *   "errors": {
     *     "email": ["The email has already been taken."],
     *     "password": ["The password must be at least 8 characters."]
     *   }
     * }
     *
     * @responseField access_token string Bearer token for API authentication
     * @responseField token_type string Type of token (always "Bearer")
     * @responseField message string Error message when registration fails
     * @responseField errors object Validation errors with field-specific messages
     *
     * @group Authentication
     */
    #[unauthenticated]
    public function create(Register $request)
    {
        $validatedData = $request->validated();

        $user = User::create([
            "name" => $validatedData["name"],
            "email" => $validatedData["email"],
            "phone" => $validatedData["phone"],
            "password" => Hash::make($validatedData["password"]),
            "status" => "Active",
        ]);

        if ($user) {
            $token = $user->createToken("auth_token")->plainTextToken;

            return response()->json([
                "access_token" => $token,
                "token_type" => "Bearer",
            ]);
        } else {
            return response()->json(
                [
                    "message" => "Invalid login details",
                ],
                401
            );
        }
    }
    /**
     * User Login
     *
     * Authenticates a user with email and password, returning an access token and user information.
     * The token expires after 24 hours. Also includes user permissions and attendance status if applicable.
     *
     * @bodyParam email string required The email address of the user. Example: <EMAIL>
     * @bodyParam password string required The password for the user account. Example: securepassword123
     *
     * @response 200 scenario="Successful login" {
     *   "user": {
     *     "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *     "name": "John Doe",
     *     "email": "<EMAIL>",
     *     "phone": "+**********",
     *     "status": "Active",
     *     "is_employee": false,
     *     "cafe_id": null,
     *     "created_at": "2024-01-15T09:00:00.000000Z",
     *     "updated_at": "2024-01-15T09:00:00.000000Z"
     *   },
     *   "access_token": "1|abcdef123456789...",
     *   "permissions": "attendance-create",
     *   "attendance": "checkin"
     * }
     *
     * @response 401 scenario="Invalid credentials" {
     *   "message": "Invalid Credentials"
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The email field is required.",
     *   "errors": {
     *     "email": ["The email field is required."],
     *     "password": ["The password field is required."]
     *   }
     * }
     *
     * @responseField user object User information object
     * @responseField user.id string UUID of the user
     * @responseField user.name string Full name of the user
     * @responseField user.email string Email address of the user
     * @responseField user.phone string Phone number of the user
     * @responseField user.status string Account status (Active/Inactive)
     * @responseField user.is_employee boolean Whether the user is an employee
     * @responseField user.cafe_id string|null UUID of associated cafe (if any)
     * @responseField access_token string Bearer token for API authentication (expires in 24 hours)
     * @responseField permissions string|null User permissions (e.g., "attendance-create")
     * @responseField attendance string|null Current attendance status ("checkin"/"checkout")
     *
     * @group Authentication
     */
    #[unauthenticated]
    public function login(Login $request)
    {
        $loginData = $request->validated();

        $user = User::where("email", $loginData["email"])
            ->where("status", "Active")
            ->first();
        if ($user) {
            if ($user->validateForApiPasswordGrant($loginData["password"])) {
                $accessToken = $user->createToken(
                    "userToken",
                    expiresAt: now()->addDays(1)
                )->plainTextToken;

                if ($user->can("attendance-create")) {
                    $permissions = "attendance-create";
                    $attendance = AttendanceEvent::where("user_id", $user->id)
                        ->latest()
                        ->first();
                }
                unset($user->roles);
                unset($user->permissions);

                return response()->json([
                    "user" => $user,
                    "access_token" => $accessToken,
                    "permissions" => $permissions ?? null,
                    "attendance" => $attendance->type ?? null,
                ]);
            }
        }
        return response()->json(["message" => "Invalid Credentials"], 401);
    }

    /**
     * Get Current User
     *
     * Returns the currently authenticated user's information including all profile details.
     * Requires a valid Bearer token in the Authorization header.
     *
     * @response 200 scenario="Successful request" {
     *   "id": "9d2e8f1a-4b5c-6d7e-8f9a-0b1c2d3e4f5g",
     *   "name": "John Doe",
     *   "email": "<EMAIL>",
     *   "phone": "+**********",
     *   "status": "Active",
     *   "is_employee": false,
     *   "cafe_id": null,
     *   "created_at": "2024-01-15T09:00:00.000000Z",
     *   "updated_at": "2024-01-15T09:00:00.000000Z",
     *   "profile_photo_url": "https://ui-avatars.com/api/?name=John+Doe&color=7F9CF5&background=EBF4FF"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @responseField id string UUID of the user
     * @responseField name string Full name of the user
     * @responseField email string Email address of the user
     * @responseField phone string Phone number of the user
     * @responseField status string Account status (Active/Inactive)
     * @responseField is_employee boolean Whether the user is an employee
     * @responseField cafe_id string|null UUID of associated cafe (if any)
     * @responseField created_at string ISO 8601 timestamp of account creation
     * @responseField updated_at string ISO 8601 timestamp of last update
     * @responseField profile_photo_url string URL to the user's profile photo
     *
     * @group Authentication
     */
    #[Authenticated]
    public function me()
    {
        return auth()->user();
    }

    /**
     * User Logout
     *
     * Logs out the current user by deleting their current access token.
     * This invalidates the token used for the request, requiring re-authentication for future API calls.
     *
     * @response 200 scenario="Successful logout" {
     *   "message": "Logged out"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @responseField message string Confirmation message of successful logout
     *
     * @group Authentication
     */
    #[Authenticated]
    public function logout(Request $request)
    {
        $request
            ->user()
            ->currentAccessToken()
            ->delete();

        return response()->json(["message" => "Logged out"]);
    }

    /**
     * Send Password Reset Link
     *
     * Sends a password reset link to the user's email address. The user will receive
     * an email with a secure token that can be used to reset their password.
     *
     * @bodyParam email string required The email address of the user requesting password reset. Must be a valid email that exists in the system. Example: <EMAIL>
     *
     * @response 200 scenario="Reset link sent successfully" {
     *   "status": "We have emailed your password reset link!"
     * }
     *
     * @response 400 scenario="Email not found" {
     *   "email": "We can't find a user with that email address."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The email field is required.",
     *   "errors": {
     *     "email": ["The email field is required."]
     *   }
     * }
     *
     * @responseField status string Success message when reset link is sent
     * @responseField email string Error message when email is not found
     *
     * @group Authentication
     */
    #[unauthenticated]
    public function forgotPassword(Request $request)
    {
        $request->validate(["email" => "required|email"]);

        $status = Password::sendResetLink($request->only("email"));

        return $status === Password::RESET_LINK_SENT
            ? response()->json(["status" => __($status)])
            : response()->json(["email" => __($status)], 400);
    }

    /**
     * Reset User Password
     *
     * Resets a user's password using a valid reset token received via email.
     * The token must be valid and not expired, and the new password must meet security requirements.
     *
     * @bodyParam email string required The email address of the user. Must match the email associated with the reset token. Example: <EMAIL>
     * @bodyParam password string required The new password for the user account. Must be at least 8 characters. Example: newsecurepassword123
     * @bodyParam password_confirmation string required Confirmation of the new password. Must match the password field. Example: newsecurepassword123
     * @bodyParam token string required The password reset token received via email. Example: abc123def456ghi789
     *
     * @response 200 scenario="Password reset successfully" {
     *   "message": "Password reset successfully"
     * }
     *
     * @response 400 scenario="Invalid token" {
     *   "message": "Invalid token"
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The password confirmation does not match.",
     *   "errors": {
     *     "email": ["The selected email is invalid."],
     *     "password": ["The password must be at least 8 characters.", "The password confirmation does not match."],
     *     "token": ["The token field is required."]
     *   }
     * }
     *
     * @responseField message string Success or error message
     * @responseField errors object Validation errors with field-specific messages
     *
     * @group Authentication
     */
    #[unauthenticated]
    public function resetPassword(Request $request)
    {
        $request->validate([
            "email" => "required|email|exists:users,email",
            "password" => "required|min:8|confirmed",
            "token" => "required",
        ]);

        $query = DB::table("password_reset_tokens")
            ->select("*")
            ->where("email", $request->email);

        $token = $query->first();

        if ($token && Hash::check($request->token, $token->token)) {
            $user = User::where("email", $request->email)->first();

            if ($user) {
                $user->password = Hash::make($request->password);
                $user->save();
                $query->delete();
                return response()->json([
                    "message" => "Password reset successfully",
                ]);
            }
        }
        return response()->json(["message" => "Invalid token"], 400);
    }

    /**
     * Register Device Token
     *
     * Registers or updates a device token for push notifications. This token is used to send
     * push notifications to the user's device. If the token exists for another user, it will be
     * transferred to the current user.
     *
     * @bodyParam access_token string required The device token for push notifications (FCM token). Example: dGhpcyBpcyBhIGZha2UgdG9rZW4gZm9yIGV4YW1wbGU
     *
     * @response 200 scenario="Token registered successfully" {
     *   "message": "Device token updated successfully"
     * }
     *
     * @response 400 scenario="Error occurred" {
     *   "message": "An error occurred during the process of set device token",
     *   "error": {}
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The access token field is required.",
     *   "errors": {
     *     "access_token": ["The access token field is required."]
     *   }
     * }
     *
     * @responseField message string Success or error message
     * @responseField error object Error details when an exception occurs
     *
     * @group Authentication
     */
    #[Authenticated]
    public function deviceTokens(Request $request)
    {
        $validatedData = $request->validate([
            "access_token" => "required|string",
        ]);

        try {
            DeviceTokens::where("user_id", "!=", $request->user()->id)
                ->where("token", $validatedData["access_token"])
                ->delete();

            $deviceToken = DeviceTokens::where("user_id", $request->user()->id)
                ->where("token", $validatedData["access_token"])
                ->first();

            if (!$deviceToken) {
                // Token doesn't exist, insert a new record
                $deviceToken = new DeviceTokens();
                $deviceToken->user_id = $request->user()->id;
                $deviceToken->token = $validatedData["access_token"];
            }
            $deviceToken->last_seen = Carbon::now();
            $deviceToken->save();

            return response()->json([
                "message" => "Device token updated successfully",
            ]);
        } catch (\Exception $e) {
            return response()->json(
                [
                    "message" =>
                        "An error occurred during the process of set device token",
                    "error" => $e,
                ],
                400
            );
        }
    }

    /**
     * Delete Device Token
     *
     * Removes a device token from the user's account. This will stop push notifications
     * from being sent to the specified device. Useful when a user logs out from a device
     * or uninstalls the app.
     *
     * @bodyParam access_token string required The device token to be removed. Must match an existing token for the authenticated user. Example: dGhpcyBpcyBhIGZha2UgdG9rZW4gZm9yIGV4YW1wbGU
     *
     * @response 200 scenario="Token deleted successfully" {
     *   "message": "Device token deleted successfully"
     * }
     *
     * @response 400 scenario="Error occurred" {
     *   "message": "An error occurred during the process of delete device token",
     *   "error": {}
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The access token field is required.",
     *   "errors": {
     *     "access_token": ["The access token field is required."]
     *   }
     * }
     *
     * @responseField message string Success or error message
     * @responseField error object Error details when an exception occurs
     *
     * @group Authentication
     */
    #[Authenticated]
    public function deleteToken(Request $request)
    {
        $validatedData = $request->validate([
            "access_token" => "required|string",
        ]);

        try {
            DeviceTokens::where("user_id", $request->user()->id)
                ->where("token", $validatedData["access_token"])
                ->delete();

            return response()->json([
                "message" => "Device token deleted successfully",
            ]);
        } catch (\Exception $e) {
            return response()->json(
                [
                    "message" =>
                        "An error occurred during the process of delete device token",
                    "error" => $e,
                ],
                400
            );
        }
    }
}
