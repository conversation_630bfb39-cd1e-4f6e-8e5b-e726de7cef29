<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Authenticated;
use Illuminate\Support\Facades\Password;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Unauthenticated;

class ProfileControler extends Controller
{
    /**
     * Update User Profile
     *
     * Updates the authenticated user's profile information including name, email, and phone.
     * If the email is changed, all user tokens are invalidated and an email verification
     * is sent to the new email address.
     *
     * @bodyParam name string required The user's full name. Must be at least 6 characters. Example: <PERSON>
     * @bodyParam email string required The user's email address. Must be a valid email format. Example: <EMAIL>
     * @bodyParam phone string required The user's phone number. Must be exactly 10 digits starting with 09. Example: **********
     *
     * @response 200 scenario="Profile updated successfully (no email change)" {
     *   "message": "Profile updated successfully"
     * }
     *
     * @response 200 scenario="Profile updated with email change" {
     *   "message": "Profile updated successfully, please check your email to verify your account",
     *   "email": "<EMAIL>"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The name field must be at least 6 characters.",
     *   "errors": {
     *     "name": ["The name field must be at least 6 characters."],
     *     "email": ["The email field must be a valid email address."],
     *     "phone": ["The phone field must start with 09."]
     *   }
     * }
     *
     * @responseField message string Success message indicating the update status
     * @responseField email string The new email address (only when email is changed)
     *
     * @group Profile
     */
    #[Authenticated]
    public function updateProfile(Request $request)
    {
        $user = Auth()->user();

        $validateData = $request->validate([
            "name" => "required|string|min:6",
            "email" => "required|email",
            "phone" =>
                "required|string|min:10|max:10|starts_with:09|unique:users,phone," .
                $user->id,
        ]);

        $oldEmail = $user->email;

        $user->update([
            "name" => $validateData["name"],
            "phone" => $validateData["phone"],
            "email" => $validateData["email"],
        ]);

        // if user change email, delete all user tokens
        if ($oldEmail != $validateData["email"]) {
            $user->tokens()->delete();
            $user->sendEmailVerificationNotification();
            return response()->json([
                "message" =>
                    "Profile updated successfully, please check your email to verify your account",
                "email" => $validateData["email"],
            ]);
        }

        return response()->json([
            "message" => "Profile updated successfully",
        ]);
    }

    /**
     * Update Profile Photo
     *
     * Updates the authenticated user's profile photo. The previous photo is automatically
     * deleted from storage when a new photo is uploaded. The photo must be an image file
     * and cannot exceed 2MB in size.
     *
     * @bodyParam photo file required The profile photo image file. Must be an image (jpg, png, gif, etc.) and maximum 2MB in size.
     *
     * @response 200 scenario="Photo uploaded successfully" {
     *   "message": "Photo uploaded successfully",
     *   "profile_photo_url": "https://example.com/storage/profile-photos/user-123.jpg"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The photo field is required.",
     *   "errors": {
     *     "photo": ["The photo field is required.", "The photo must be an image.", "The photo may not be greater than 2048 kilobytes."]
     *   }
     * }
     *
     * @responseField message string Success message confirming the photo upload
     * @responseField profile_photo_url string URL to the newly uploaded profile photo
     *
     * @group Profile
     */
    #[Authenticated]
    public function updatePhoto(Request $request)
    {
        $request->validate([
            "photo" => "required|image|max:2048",
        ]);

        $user = auth()->user();

        if ($user->profile_photo_path) {
            Storage::delete($user->profile_photo_path);
        }

        $user->updateProfilePhoto($request->file("photo"));
        $user->save();

        return response()->json([
            "message" => "Photo uploaded successfully",
            "profile_photo_url" => $user->profile_photo_url,
        ]);
    }

    /**
     * Change Password
     *
     * Changes the authenticated user's password. For security reasons, all existing
     * authentication tokens are invalidated after the password change, requiring
     * the user to log in again with the new password.
     *
     * @bodyParam password string required The new password. Must be at least 8 characters long. Example: newSecurePassword123
     *
     * @response 200 scenario="Password changed successfully" {
     *   "message": "Password updated successfully.  Please login again"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The password field must be at least 8 characters.",
     *   "errors": {
     *     "password": ["The password field must be at least 8 characters."]
     *   }
     * }
     *
     * @responseField message string Success message indicating password change and requirement to log in again
     *
     * @group Profile
     */
    #[Authenticated]
    public function changePassword(Request $request)
    {
        $validateData = $request->validate([
            "password" => "required|string|min:8",
        ]);

        $user = Auth()->user();

        $user->update([
            "password" => Hash::make($validateData["password"]),
        ]);

        $user->tokens()->delete();

        return response()->json([
            "message" => "Password updated successfully.  Please login again",
        ]);
    }

    /**
     * Disable User Account
     *
     * Disables the authenticated user's account by setting status to inactive and
     * removing personal information (phone and email). This action requires password
     * confirmation and invalidates all authentication tokens.
     *
     * @bodyParam password string required The user's current password for confirmation. Must be at least 8 characters. Example: currentPassword123
     *
     * @response 200 scenario="Account disabled successfully" {
     *   "message": "User disabled successfully"
     * }
     *
     * @response 200 scenario="Invalid password" {
     *   "message": "Invalid password"
     * }
     *
     * @response 401 scenario="Unauthenticated" {
     *   "message": "Unauthenticated."
     * }
     *
     * @response 422 scenario="Validation error" {
     *   "message": "The password field must be at least 8 characters.",
     *   "errors": {
     *     "password": ["The password field must be at least 8 characters."]
     *   }
     * }
     *
     * @responseField message string Success or error message indicating the result of the disable operation
     *
     * @group Profile
     */
    #[Authenticated]
    public function disableUser(Request $request)
    {
        $validateData = $request->validate([
            "password" => "required|string|min:8",
        ]);

        $user = Auth()->user();

        if (!$user->validateForApiPasswordGrant($validateData["password"])) {
            return response()->json([
                "message" => "Invalid password",
            ]);
        }

        $user->update([
            "phone" => null,
            "email" => null,
            "status" => "Inactive",
        ]);
        $user->save();
        $user->tokens()->delete();
        return response()->json([
            "message" => "User disabled successfully",
        ]);
    }
}
