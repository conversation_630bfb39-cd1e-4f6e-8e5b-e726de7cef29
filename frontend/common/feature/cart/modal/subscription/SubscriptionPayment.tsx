//hooks
import useTranslation from 'next-translate/useTranslation';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';

//components
import { Box, Grid, Loader, Select, Text, createStyles } from '@mantine/core';

//redux
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { getSubscriptionInfo } from '@/redux/client/balance/BalanceSlice';

//API
import service from '@/api/service';
import ProductSubscription from './ProductSubscription';
import BalanceSubscription from './BalanceSubscription';
import { paymentRequest, reset } from '@/redux/client/cart/CartSlice';
import SystemInfo from '@/common/components/containers/systemInfo';
import AlertMessage from '@/common/components/DataViewers/AlertMessage';
import ButtonWithLoader from '@/common/components/buttons/ButtonWithLoader';

const useStyles = createStyles(() => ({
  notEnough: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: 250,
    border: '1px solid #ECECEC',
    borderRadius: 8,
    gap: 20,
    padding: 15,
  },

  warningIcon: {
    borderRadius: 15,
    width: 53,
    height: 53,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFDBD7',
  },

  loading: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: 300,
    border: '1px solid #ECECEC',
    borderRadius: 8,
    gap: 14,
    padding: 15,
  },
}));

interface SubscriptionPaymentProps {
  balanceTotal: number;
  subscriptionBalanceTotal: number;
  orderTotal: number;
  setPaymentFailed: any;
  setOrderComplete: Dispatch<SetStateAction<boolean>>;
  isLoading?: boolean;
}

export default function SubscriptionPayment(props: SubscriptionPaymentProps) {
  const {
    balanceTotal,
    subscriptionBalanceTotal,
    orderTotal,
    setPaymentFailed,
    setOrderComplete,
    isLoading,
  } = props;

  const { t } = useTranslation('common');
  const { classes } = useStyles();

  const [type, setType] = useState<string | null>('0');
  const [covers, setCovers] = useState(false);
  const [productSubscriptionLoader, setProductSubscriptionLoader] =
    useState(false);

  const cartStore = useAppSelector((state) => state.client.cart);
  const dispatch = useAppDispatch();

  const canBuy = () => {
    return (
      subscriptionBalanceTotal >= orderTotal ||
      subscriptionBalanceTotal + balanceTotal >= orderTotal
    );
  };

  const checkout = async () => {
    dispatch(
      paymentRequest({
        paymentMethod:
          type === '0'
            ? 'product_subscription'
            : orderTotal > subscriptionBalanceTotal
              ? 'mixed'
              : 'subscription',
        place: cartStore.place,
        notes: cartStore.notes,
        products: cartStore.items,
      }),
    );

    dispatch(getSubscriptionInfo());
  };

  useEffect(() => {
    setProductSubscriptionLoader(true);
    async function subscriptionCovers() {
      try {
        const result = await service.postRequest(
          'order/check-product-subscription',
          {
            data: {
              products: cartStore.items,
            },
          },
        );
        if (result.message !== 'Insufficient balance') setType('0');
        setCovers(true);
      } catch (error) {
        setType('1');
        setCovers(false);
      }
      setProductSubscriptionLoader(false);
    }
    subscriptionCovers();
  }, [cartStore.items]);

  useEffect(() => {
    if (cartStore.isState.isSent) {
      if (cartStore.isState.isSuccess) {
        dispatch(reset());
        setPaymentFailed(false);
      } else {
        setPaymentFailed(true);
      }
      setOrderComplete(true);
    }
  }, [
    cartStore.isState.isSent,
    cartStore.isState.isSuccess,
    dispatch,
    setOrderComplete,
    setPaymentFailed,
  ]);

  if (!covers && (!canBuy() || subscriptionBalanceTotal === 0)) {
    return <AlertMessage message={t('cart.modal.subscription.notAvailable')} />;
  }

  if (isLoading || productSubscriptionLoader) {
    return (
      <Box className={classes.loading}>
        <Loader />
      </Box>
    );
  } else
    return (
      <>
        <Grid.Col span={12}>
          <Select
            data={
              covers
                ? canBuy() && subscriptionBalanceTotal > 0
                  ? [
                      { label: 'اشتراك منتج', value: '0' },
                      { label: 'اشتراك رصيد', value: '1' },
                    ]
                  : [{ label: 'اشتراك منتج', value: '0' }]
                : [{ label: 'اشتراك رصيد', value: '1' }]
            }
            value={type}
            onChange={setType}
            data-cy='subscriptionType'
            sx={{
              input: { border: '1px solid #ECECEC' },
              '[data-selected=true]': {
                backgroundColor: '#ECECEC !important',
                color: 'gray !important',
              },
            }}
          />
        </Grid.Col>

        <Grid.Col span={12}>
          {type === '0' ? (
            <ProductSubscription orderTotal={orderTotal} />
          ) : (
            <BalanceSubscription
              orderTotal={orderTotal}
              balanceTotal={balanceTotal}
              subscriptionBalanceTotal={subscriptionBalanceTotal}
            />
          )}
        </Grid.Col>

        <Grid.Col span={12}>
          <SystemInfo>
            <Text c='red.4' weight={700} align='center'>
              {t('cart.modal.subscription.warning')}
            </Text>
          </SystemInfo>
        </Grid.Col>

        <Grid.Col span={12}>
          {/* <SubmitButton
          title={t('cart.modal.confirm')}
          buttonColor='green.8'
          fullWidth={true}
          handleClick={checkout}
          data-cy='checkoutBtn'
        /> */}
          <ButtonWithLoader
            title={t('cart.modal.confirm')}
            buttonColor='green.8'
            fullWidth={true}
            handleClick={checkout}
            data-cy='checkoutBtn'
            loading={cartStore.isState.isLoading}
            loaderColor='#ffffff'
          />
        </Grid.Col>
      </>
    );
}
