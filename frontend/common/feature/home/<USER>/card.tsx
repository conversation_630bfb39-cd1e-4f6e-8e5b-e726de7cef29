'use client';

//hooks
import { useEffect, useState } from 'react';
import useTranslation from 'next-translate/useTranslation';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';

//api
import { addItem, openCart } from '@/redux/client/cart/CartSlice';

//components
import {
  Card,
  Image,
  ActionIcon,
  Group,
  Text,
  Flex,
  Button,
  Badge,
  Box,
  useMantineTheme,
  Tooltip,
} from '@mantine/core';
import { CardsCarousel } from '@/common/feature/home/<USER>/carousels';

//assets
import useStyles from './style';
import {
  IconBookmark,
  IconBookmarkFilled,
  IconCheck,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { removeProductName } from '@/utils/removeProductName';
import { roundToThreeSignificantFigures } from '@/utils/round';
import { useViewportSize } from '@mantine/hooks';

interface ProductCardProps {
  variations: any[];
  productId: string;
  productName: string;
  productStatus: 'Active' | 'Inactive';
  category: string;
  featured: 'featured' | 'special' | 'sale' | 'new' | 'regular';
  isFavorite: boolean;
  checkin: boolean;
  handleFavorite: (productId: string, favorited: boolean) => void;
}

export default function ProductCard(props: ProductCardProps) {
  const {
    variations,
    productId,
    productName,
    category,
    featured,
    isFavorite,
    checkin,
    handleFavorite,
    productStatus,
  } = props;
  const { t } = useTranslation('home');
  const { width } = useViewportSize();

  const { classes } = useStyles();

  const [active, setActive] = useState(0);
  const [unavailable, setUnavailable] = useState(false);

  const authStore = useAppSelector((state) => state.auth);
  const cartStore = useAppSelector((state) => state.client.cart);

  const theme = useMantineTheme();
  const dispatch = useAppDispatch();

  let featuredColors = {
    featured: 'lime',
    regular: 'gray',
    special: 'yellow',
    sale: 'red',
    new: 'green',
  };

  const getPrice = (item: any) => {
    if (!item) {
      return '';
    }
    if (authStore.user?.user?.is_employee !== 'No') {
      return item.emp_price;
    } else {
      return item.cus_price;
    }
  };

  const handleAddToCart = async () => {
    dispatch(
      addItem({
        id: variations[active].id,
        name: removeProductName(
          `${productName} ${variations[active].name}`,
          productName,
        ),
        price: getPrice(variations[active]),
        qty: 1,
        image: variations[active].image,
      }),
    );

    // Open cart If sccren size is big and cart is empty
    if (cartStore.items.length === 0 && width > 1000) {
      dispatch(openCart());
    } else if (!cartStore.isOpen) {
      notifications.show({
        title: t('addedToCartTitle'),
        message: t('addedToCartMsg'),
        icon: <IconCheck />,
        withBorder: true,
      });
    }
  };

  useEffect(() => {
    const index = variations.findIndex((item: any) => item.status === 'Active');
    if (productStatus === 'Inactive' || index === -1) {
      setActive(0);
      setUnavailable(true);
    } else {
      setActive(index);
      setUnavailable(false);
    }
  }, [productStatus, variations]);

  return (
    <Card className={classes.card} padding='lg' radius='md'>
      <Card.Section
        p='sm'
        sx={{
          position: 'relative',
        }}
      >
        <Image
          src={variations[active]?.image}
          alt={variations[active]?.name}
          height={200}
          radius='md'
          withPlaceholder
          fit='contain'
        />
        {featured?.toLocaleLowerCase() !== 'regular' && (
          <Badge
            variant='light'
            c={theme.colors[featuredColors[featured]][6]}
            leftSection={
              <Box
                sx={(theme) => ({
                  width: 10,
                  height: 10,
                  backgroundColor: theme.colors[featuredColors[featured]][6],
                  borderRadius: '50%',
                })}
              />
            }
            bg={theme.colors[featuredColors[featured]][1]}
            className={classes.badge}
          >
            {t(`productFeatured.${featured}`)}
          </Badge>
        )}
      </Card.Section>

      <Flex
        mih={30}
        justify='space-between'
        align='flex-start'
        direction='row'
        wrap='nowrap'
      >
        <Box w={'80%'}>
          <Tooltip label={productName}>
            <Text className={classes.title} truncate={'end'}>
              {productName}
            </Text>
          </Tooltip>
        </Box>
        <Text className={classes.price}>
          {unavailable
            ? ''
            : roundToThreeSignificantFigures(getPrice(variations[active]))}
          <span
            style={{
              fontSize: 10,
              color: 'gray',
            }}
          >
            {unavailable ? '' : ' د.ل '}
          </span>
        </Text>
      </Flex>

      {unavailable ? (
        <Box className={classes.notAvailable}>{t('unavailable')}</Box>
      ) : (
        <CardsCarousel
          productName={productName}
          variations={variations}
          active={active}
          setActive={setActive}
          categoryId={category}
        />
      )}
      <Group mt='auto'>
        <Button
          radius='md'
          color='green.8'
          disabled={unavailable || checkin}
          onClick={handleAddToCart}
          style={{
            flex: 1,
          }}
          data-cy='addToCart'
        >
          {t('AddToCart')}
        </Button>
        <ActionIcon
          style={{ borderColor: theme.colors.green[8], borderWidth: 1.5 }}
          variant='default'
          radius='md'
          size={36}
          onClick={() => handleFavorite(productId, isFavorite)}
          data-cy='favoriteBtn'
        >
          {!isFavorite ? (
            <IconBookmark
              color={theme.colors.green[8]}
              size={26}
              strokeWidth={1.5}
            />
          ) : (
            <IconBookmarkFilled
              size={26}
              strokeWidth={1.5}
              style={{ color: theme.colors.green[8] }}
            />
          )}
        </ActionIcon>
      </Group>
    </Card>
  );
}
