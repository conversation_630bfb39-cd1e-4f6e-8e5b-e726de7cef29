//hooks
import useTranslation from 'next-translate/useTranslation';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';

//api
import {
  changeVariationStatus,
  changeProductStatus,
  getProducts,
} from '@/redux/common/products/ProductSlice';

//components
import { Button, Center, Grid, createStyles } from '@mantine/core';
import Product from './product';
import AppLoader from '@/common/components/Loader/loader';
import NoneFound from '@/common/components/containers/noneFound';
import { useViewportSize } from '@mantine/hooks';
import { useEffect } from 'react';
import { Notifications } from '@mantine/notifications';

const useStyles = createStyles((theme) => ({
  noProductsContainer: {
    height: '60vh',
    backgroundColor: '#fff',
    borderRadius: 8,
    border: '1px solid #ECECEC',
  },
}));

export default function Products({ active }: { active: any }) {
  const { t } = useTranslation('products');
  const { classes } = useStyles();

  const product = useAppSelector((state) => state.product);
  const dispatch = useAppDispatch();

  let newProducts =
    product?.products?.data && product?.products?.data?.length > 0
      ? product?.products?.data
      : [];

  const handleFetchMore = () => {
    let prods = product.products;
    let page =
      prods?.current_page !== prods?.last_page
        ? prods?.current_page + 1
        : prods?.current_last_pagepage;

    if (active !== '') {
      dispatch(getProducts({ page, categoryId: active }));
    } else {
      dispatch(getProducts({ page }));
    }
  };

  const handleVariation = (item: any) => {
    let state = item.status === 'Active' ? 'Inactive' : 'Active';

    let data = {
      variation_id: item.id,
      product_id: item.product_id,
      status: state,
    };

    dispatch(changeVariationStatus(data));
  };

  const handleProduct = (item: any, isActiveVariant: boolean) => {
    let state = item.status === 'Active' ? 'Inactive' : 'Active';

    let data = {
      id: item.id,
      status: state,
    };

    dispatch(changeProductStatus(data));
  };

  const { width } = useViewportSize();
  const spanSize =
    width >= 1580
      ? 3
      : width >= 1220
        ? 4
        : width >= 720
          ? 6
          : width >= 390
            ? 12
            : 12;

  useEffect(() => {
    if (product.isState.isError) {
      Notifications.show({
        message: t('errors.productStatus'),
        color: 'red',
      });
    }
  }, [product.isState.isError, t]);

  return (
    <div>
      <Grid gutter='xl'>
        {newProducts && newProducts?.length > 0 ? (
          <>
            {product?.products?.data.map((product: any) => {
              return (
                <Grid.Col
                  span={spanSize}
                  // xl={3}
                  // lg={4}
                  // md={6}
                  // sm={6}
                  // xs={12}
                  key={product?.id}
                >
                  <Product
                    data={product}
                    handleVariation={handleVariation}
                    handleProduct={handleProduct}
                  />
                </Grid.Col>
              );
            })}

            {product?.products?.data?.length !== product?.products?.total ? (
              <Grid.Col span={12}>
                <Center>
                  {product?.isState.isLoadingAll ? (
                    <AppLoader />
                  ) : (
                    <Button
                      variant='outline'
                      color='green.8'
                      w={200}
                      onClick={handleFetchMore}
                    >
                      {t('showMore')}
                    </Button>
                  )}
                </Center>{' '}
              </Grid.Col>
            ) : null}
          </>
        ) : product?.isState.isLoadingAll ? (
          <Grid.Col span={12} className={classes.noProductsContainer}>
            <AppLoader />
          </Grid.Col>
        ) : (
          <NoneFound message={t('noProducts')} span={12} />
        )}
      </Grid>
    </div>
  );
}
